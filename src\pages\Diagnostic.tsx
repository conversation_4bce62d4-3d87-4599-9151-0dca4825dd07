import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import PurchaseAssistant from "@/components/PurchaseAssistant";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowRight, 
  BarChart2, 
  FileSpreadsheet, 
  CheckCircle, 
  Lock, 
  Unlock, 
  ShoppingCart, 
  CreditCard,
  LightbulbIcon,
  TrendingUpIcon,
  SearchCheckIcon,
  InfoIcon
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";

const pricingDetails = {
  valuation: {
    price: 1297,
    title: "Diagnóstico de Valuation",
    description: "Descubra o valor estimado do seu negócio com nossa ferramenta de diagnóstico baseada em métricas de mercado e indicadores financeiros.",
    features: [
      "Análise de indicadores financeiros",
      "Comparativo com mercado",
      "Projeção de crescimento",
      "Relatório detalhado em PDF",
      "30 minutos de mentoria pós-diagnóstico"
    ]
  },
  maturity: {
    price: 347,
    title: "Assessment de Maturidade",
    description: "Identifique o nível de maturidade da sua startup e receba recomendações personalizadas para avançar para o próximo estágio.",
    features: [
      "Análise de processo e operações",
      "Avaliação de modelo de negócio",
      "Estratégia de crescimento",
      "Relatório detalhado em PDF",
      "30 minutos de mentoria pós-diagnóstico"
    ]
  },
  bundle: {
    price: Math.round(((1297 + 347) * 0.8)),
    discount: 20,
    title: "Pacote Completo",
    description: "Adquira ambos os diagnósticos por um preço especial e tenha uma visão completa do seu negócio.",
    features: [
      "Todos os benefícios do Diagnóstico de Valuation",
      "Todos os benefícios do Assessment de Maturidade",
      "60 minutos de mentoria pós-diagnóstico",
      "Acesso a comunidade exclusiva por 3 meses",
      "Desconto de 20% em relação à compra individual"
    ]
  }
};

const Diagnostic = () => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isCheckout, setIsCheckout] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Diagnóstico | ATAC Academy";
  }, []);

  const handleProceedToCheckout = () => {
    if (!selectedPlan) {
      toast({
        title: "Selecione um plano",
        description: "Por favor, selecione um plano para continuar.",
        variant: "destructive",
      });
      return;
    }
    
    setIsCheckout(true);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleFinishPurchase = () => {
    toast({
      title: "Compra realizada com sucesso!",
      description: "Você receberá um email com os detalhes de acesso ao(s) diagnóstico(s).",
    });
    
    setTimeout(() => {
      if (selectedPlan === "valuation") {
        navigate("/valuation");
      } else if (selectedPlan === "maturity") {
        navigate("/maturity");
      } else if (selectedPlan === "bundle") {
        navigate("/valuation");
      } else {
        navigate("/diagnostic");
      }
    }, 2000);
  };

  if (isCheckout) {
    const selectedPackage = selectedPlan === "bundle" 
      ? pricingDetails.bundle 
      : selectedPlan === "maturity" 
        ? pricingDetails.maturity 
        : pricingDetails.valuation;

    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="pt-28 pb-20">
          <div className="container mx-auto px-4 max-w-4xl">
            <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
              <div className="flex justify-between items-center mb-8">
                <h1 className="font-montserrat text-2xl md:text-3xl font-bold text-gray-900">
                  Finalizar compra
                </h1>
                <Button 
                  variant="ghost" 
                  onClick={() => setIsCheckout(false)}
                  className="text-gray-500"
                >
                  Voltar
                </Button>
              </div>
              
              <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                <h2 className="font-semibold text-lg mb-4">Resumo do pedido</h2>
                <div className="flex justify-between items-center mb-2">
                  <span>{selectedPackage.title}</span>
                  <span className="font-medium">R$ {selectedPackage.price.toFixed(2)}</span>
                </div>
                {selectedPlan === "bundle" && (
                  <div className="text-sm text-green-600 mb-4">
                    Economia de R$ {(pricingDetails.valuation.price + pricingDetails.maturity.price - pricingDetails.bundle.price).toFixed(2)} (15% de desconto)
                  </div>
                )}
                <div className="border-t border-gray-200 my-4 pt-4 flex justify-between font-bold">
                  <span>Total</span>
                  <span>R$ {selectedPackage.price.toFixed(2)}</span>
                </div>
              </div>
              
              <div className="space-y-6">
                <h2 className="font-semibold text-lg">Dados de pagamento</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Nome no cartão</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="Nome no cartão"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Número do cartão</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="1234 5678 9012 3456"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Data de validade</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="MM/AA"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">CVV</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="123"
                    />
                  </div>
                </div>
                
                <div className="pt-6 mt-6 border-t border-gray-200">
                  <Button 
                    onClick={handleFinishPurchase}
                    className="w-full py-6 text-lg bg-brand hover:bg-brand-secondary flex items-center justify-center"
                  >
                    <CreditCard className="mr-2 h-5 w-5" />
                    Finalizar Pagamento
                  </Button>
                  <p className="text-center text-sm text-gray-500 mt-4">
                    Seus dados estão protegidos com criptografia de ponta a ponta
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-28 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h1 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Diagnóstico <span className="text-brand">Empresarial</span>
            </h1>
            <p className="text-xl text-gray-700">
              Avalie o estágio do seu negócio e descubra oportunidades estratégicas para aceleração e crescimento.
            </p>
          </div>
          
          <div className="max-w-5xl mx-auto mb-16 bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-8 md:p-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                Por que realizar um diagnóstico empresarial?
              </h2>
              
              <p className="text-gray-700 mb-8 text-lg">
                Um diagnóstico preciso é o primeiro passo para tomar decisões estratégicas 
                que impulsionam o crescimento da sua empresa. Nossos diagnósticos ajudam 
                empreendedores e gestores a identificar gargalos, potencializar forças e 
                planejar ações concretas para o futuro.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="flex items-start">
                  <div className="bg-brand/10 p-3 rounded-full mr-4">
                    <SearchCheckIcon className="h-6 w-6 text-brand" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Identifique oportunidades</h3>
                    <p className="text-gray-600">
                      Descubra áreas inexploradas e oportunidades de crescimento com base em dados concretos.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-brand-accent/10 p-3 rounded-full mr-4">
                    <BarChart2 className="h-6 w-6 text-brand-accent" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Decisões baseadas em dados</h3>
                    <p className="text-gray-600">
                      Substitua intuição por métricas e indicadores que realmente mostram o desempenho do seu negócio.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-green-100 p-3 rounded-full mr-4">
                    <TrendingUpIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Impulsione seu crescimento</h3>
                    <p className="text-gray-600">
                      Receba estratégias e planos de ação personalizados para acelerar o crescimento da sua empresa.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-blue-100 p-3 rounded-full mr-4">
                    <InfoIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Acompanhamento especializado</h3>
                    <p className="text-gray-600">
                      Conte com a mentoria de especialistas para interpretar resultados e definir os próximos passos.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Mais de 300 empreendedores já utilizaram nossos diagnósticos para:
                </p>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-3 text-gray-700">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Captar investimentos com maior facilidade
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Aumentar o valor de mercado da empresa
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Identificar gargalos operacionais
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Otimizar processos e reduzir custos
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="max-w-5xl mx-auto mb-20">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 ${selectedPlan === 'valuation' ? 'ring-4 ring-brand' : ''}`}>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-6">
                    <div className="bg-brand/10 p-4 rounded-full w-16 h-16 flex items-center justify-center">
                      <BarChart2 className="h-8 w-8 text-brand" />
                    </div>
                    <div className="text-right">
                      <span className="block text-3xl font-bold text-gray-900">R$ {pricingDetails.valuation.price}</span>
                      <span className="text-gray-500">pagamento único</span>
                    </div>
                  </div>
                  
                  <h2 className="text-2xl font-bold mb-4">{pricingDetails.valuation.title}</h2>
                  <p className="text-gray-700 mb-6">
                    {pricingDetails.valuation.description}
                  </p>
                  
                  <div className="space-y-4 mb-8">
                    {pricingDetails.valuation.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-brand mr-3 flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    onClick={() => setSelectedPlan(selectedPlan === 'valuation' ? null : 'valuation')}
                    className={`w-full py-6 ${selectedPlan === 'valuation' ? 'bg-brand hover:bg-brand-secondary' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
                  >
                    {selectedPlan === 'valuation' ? (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        Selecionado
                      </>
                    ) : (
                      <>
                        <Lock className="mr-2 h-5 w-5" />
                        Selecionar
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              <div className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 ${selectedPlan === 'maturity' ? 'ring-4 ring-brand-accent' : ''}`}>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-6">
                    <div className="bg-brand-accent/10 p-4 rounded-full w-16 h-16 flex items-center justify-center">
                      <FileSpreadsheet className="h-8 w-8 text-brand-accent" />
                    </div>
                    <div className="text-right">
                      <span className="block text-3xl font-bold text-gray-900">R$ {pricingDetails.maturity.price}</span>
                      <span className="text-gray-500">pagamento único</span>
                    </div>
                  </div>
                  
                  <h2 className="text-2xl font-bold mb-4">{pricingDetails.maturity.title}</h2>
                  <p className="text-gray-700 mb-6">
                    {pricingDetails.maturity.description}
                  </p>
                  
                  <div className="space-y-4 mb-8">
                    {pricingDetails.maturity.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-brand-accent mr-3 flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    onClick={() => setSelectedPlan(selectedPlan === 'maturity' ? null : 'maturity')}
                    className={`w-full py-6 ${selectedPlan === 'maturity' ? 'bg-brand-accent hover:bg-brand-accent/90' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
                  >
                    {selectedPlan === 'maturity' ? (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        Selecionado
                      </>
                    ) : (
                      <>
                        <Lock className="mr-2 h-5 w-5" />
                        Selecionar
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              <div className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 ${selectedPlan === 'bundle' ? 'ring-4 ring-brand' : ''}`}>
                <div className="bg-gradient-to-r from-brand to-brand-accent text-white p-3 text-center">
                  <span className="font-medium">Melhor custo-benefício</span>
                </div>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-6">
                    <div className="bg-gray-100 p-4 rounded-full w-16 h-16 flex items-center justify-center">
                      <div className="relative">
                        <BarChart2 className="h-6 w-6 text-brand absolute -top-1 -left-1" />
                        <FileSpreadsheet className="h-6 w-6 text-brand-accent absolute -bottom-1 -right-1" />
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center justify-end">
                        <span className="line-through text-gray-400 mr-2">R$ {(pricingDetails.valuation.price + pricingDetails.maturity.price).toFixed(0)}</span>
                        <Badge className="bg-green-600">-{pricingDetails.bundle.discount}%</Badge>
                      </div>
                      <span className="block text-3xl font-bold text-gray-900">R$ {pricingDetails.bundle.price}</span>
                      <span className="text-gray-500">pagamento único</span>
                    </div>
                  </div>
                  
                  <h2 className="text-2xl font-bold mb-4">{pricingDetails.bundle.title}</h2>
                  <p className="text-gray-700 mb-6">
                    {pricingDetails.bundle.description}
                  </p>
                  
                  <div className="space-y-4 mb-8">
                    {pricingDetails.bundle.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    onClick={() => setSelectedPlan(selectedPlan === 'bundle' ? null : 'bundle')}
                    className={`w-full py-6 ${selectedPlan === 'bundle' ? 'bg-gradient-to-r from-brand to-brand-accent hover:from-brand/90 hover:to-brand-accent/90' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
                  >
                    {selectedPlan === 'bundle' ? (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        Selecionado
                      </>
                    ) : (
                      <>
                        <Unlock className="mr-2 h-5 w-5" />
                        Selecionar
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="mt-12 text-center">
              <Button
                onClick={handleProceedToCheckout}
                className="bg-gray-900 hover:bg-gray-800 text-white py-6 px-10 text-lg"
                disabled={!selectedPlan}
              >
                <ShoppingCart className="mr-2 h-5 w-5" />
                Prosseguir para pagamento
              </Button>
              <p className="text-gray-500 mt-4">Pagamento 100% seguro e protegido</p>
            </div>
          </div>
          
          <div className="max-w-4xl mx-auto mt-20">
            <h2 className="text-3xl font-bold text-center mb-12">Perguntas frequentes</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="font-bold text-lg mb-3">Como funcionam os diagnósticos?</h3>
                <p className="text-gray-700">Após a compra, você terá acesso à plataforma onde poderá preencher os formulários de diagnóstico. Depois de concluído, você receberá um relatório detalhado com recomendações personalizadas.</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="font-bold text-lg mb-3">Quanto tempo leva para completar?</h3>
                <p className="text-gray-700">O preenchimento leva em média 30-40 minutos para cada diagnóstico. Recomendamos reservar um tempo adequado para responder com calma e precisão.</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="font-bold text-lg mb-3">Os resultados são confidenciais?</h3>
                <p className="text-gray-700">Sim, todos os dados fornecidos e resultados gerados são totalmente confidenciais e protegidos por nossa política de privacidade.</p>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="font-bold text-lg mb-3">Posso fazer os diagnósticos novamente?</h3>
                <p className="text-gray-700">Sim, você tem acesso ilimitado aos diagnósticos por 12 meses após a compra. Recomendamos refazer a cada 3-6 meses para acompanhar sua evolução.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <PurchaseAssistant productName="Diagnóstico Empresarial" productType="Serviço de Consultoria" />
      
      <Footer />
    </div>
  );
};

export default Diagnostic;
