# Especificações Técnicas - Portal ATAC

## Arquitetura do Sistema

### Frontend Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    PORTAL ATAC FRONTEND                     │
├─────────────────────────────────────────────────────────────┤
│  React 18 + TypeScript + Vite                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Pages     │ │ Components  │ │    Data     │          │
│  │             │ │             │ │             │          │
│  │ Dashboard   │ │ UI (shadcn) │ │ Assessment  │          │
│  │ Diagnostic  │ │ Diagnostic  │ │ Journey     │          │
│  │ Journey     │ │ Journey     │ │ Content     │          │
│  │ Content     │ │ Content     │ │ Networking  │          │
│  │ Networking  │ │ Networking  │ │ Tools       │          │
│  │ Tools       │ │ Analytics   │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Routing (React Router) | State (React Hooks)             │
│  Styling (Tailwind CSS) | Forms (React Hook Form + Zod)   │
└─────────────────────────────────────────────────────────────┘
```

### Tecnologias Utilizadas

#### Core Framework
- **React 18.3.1**: Framework principal para UI
- **TypeScript 5.5.3**: Tipagem estática
- **Vite 5.4.1**: Build tool e dev server

#### UI e Styling
- **Tailwind CSS 3.4.11**: Framework CSS utilitário
- **shadcn/ui**: Biblioteca de componentes
- **Lucide React 0.462.0**: Ícones
- **class-variance-authority**: Variantes de componentes
- **tailwind-merge**: Merge de classes Tailwind

#### Formulários e Validação
- **React Hook Form 7.53.0**: Gerenciamento de formulários
- **Zod 3.23.8**: Schema validation
- **@hookform/resolvers**: Integração Zod + RHF

#### Roteamento e Navegação
- **React Router DOM 6.26.2**: Roteamento SPA

#### Componentes UI Avançados
- **@radix-ui/***: Componentes primitivos acessíveis
- **date-fns 3.6.0**: Manipulação de datas
- **recharts 2.12.7**: Gráficos e visualizações

## Estrutura de Arquivos

### Organização do Projeto
```
src/
├── components/
│   ├── ui/                     # Componentes base (shadcn/ui)
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── input.tsx
│   │   ├── select.tsx
│   │   ├── progress.tsx
│   │   └── ...
│   ├── diagnostic/             # Componentes específicos de diagnóstico
│   ├── journey/               # Componentes de jornada personalizada
│   ├── content/               # Componentes de biblioteca de conteúdo
│   ├── networking/            # Componentes de networking
│   ├── analytics/             # Componentes de análise e métricas
│   ├── Navbar.tsx             # Navegação principal
│   ├── Footer.tsx             # Rodapé
│   └── AIAssistant.tsx        # Assistente IA
├── pages/
│   ├── Dashboard.tsx          # Dashboard principal do usuário
│   ├── Diagnostic.tsx         # Página de diagnósticos
│   ├── MaturityAssessment.tsx # Assessment de maturidade
│   ├── PersonalizedJourney.tsx # Jornada personalizada
│   ├── ContentLibrary.tsx     # Biblioteca de conteúdo
│   ├── NetworkingHub.tsx      # Hub de networking
│   ├── ManagementTools.tsx    # Ferramentas de gestão
│   └── Index.tsx              # Página inicial
├── data/
│   ├── assessmentQuestions.ts # Perguntas do assessment
│   ├── mentors.ts            # Dados de mentores
│   └── ...
├── hooks/
│   ├── use-mobile.tsx        # Hook para detecção mobile
│   └── use-toast.ts          # Hook para notificações
├── lib/
│   └── utils.ts              # Utilitários e helpers
└── App.tsx                   # Componente raiz
```

## Componentes Principais

### 1. Sistema de Diagnóstico

#### MaturityAssessment.tsx
```typescript
interface AssessmentQuestion {
  id: string;
  category: string;
  question: string;
  options: string[];
}

const categoryInfo = {
  solution: { title: "Solução", icon: Lightbulb },
  mindset: { title: "Mindset", icon: Users },
  team: { title: "Equipe", icon: Users },
  product: { title: "Produto/Serviço", icon: Target },
  project: { title: "Gestão de Projetos", icon: BarChart3 },
  pitch: { title: "Pitch", icon: MessageSquare }
};
```

**Funcionalidades**:
- Navegação por categorias
- Progresso em tempo real
- Validação de formulário com Zod
- Interface responsiva
- Tooltips explicativos

#### Diagnostic.tsx
```typescript
const pricingDetails = {
  valuation: {
    price: 1297,
    features: [...],
    benefits: [...],
    duration: "45-60 min",
    deliveryTime: "24-48h"
  },
  maturity: { ... },
  bundle: { ... }
};
```

**Funcionalidades**:
- Comparação de planos
- Sistema de checkout
- Depoimentos de clientes
- FAQ interativo
- Demonstração em vídeo

### 2. Dashboard do Usuário

#### Dashboard.tsx
```typescript
const mockUserData = {
  assessments: {
    maturity: { score: 7.2, level: "Crescimento" },
    valuation: { estimatedValue: "R$ 2.5M" }
  },
  journey: {
    currentPhase: "Validação de Mercado",
    progress: 65,
    completedTasks: 12,
    totalTasks: 18
  },
  networking: { connections: 24, events: 2 }
};
```

**Funcionalidades**:
- Cards de estatísticas
- Progresso da jornada
- Próximas ações
- Acesso rápido às funcionalidades

### 3. Jornada Personalizada

#### PersonalizedJourney.tsx
```typescript
const journeyData = {
  phases: [
    {
      id: 1,
      title: "Ideação e Conceito",
      status: "completed",
      tasks: [...],
      resources: [...]
    },
    // ... outras fases
  ]
};
```

**Funcionalidades**:
- Timeline interativa
- Tarefas por fase
- Recursos recomendados
- Navegação entre fases
- Progresso visual

### 4. Biblioteca de Conteúdo

#### ContentLibrary.tsx
```typescript
const contentLibrary = [
  {
    id: 1,
    title: "Como validar sua ideia de negócio",
    type: "article",
    category: "Validação",
    difficulty: "Iniciante",
    rating: 4.8,
    tags: ["validação", "mvp"]
  }
];
```

**Funcionalidades**:
- Sistema de filtros avançado
- Busca por texto
- Categorização por tipo e dificuldade
- Avaliações e estatísticas
- Conteúdo em destaque

### 5. Hub de Networking

#### NetworkingHub.tsx
```typescript
const networkMembers = [
  {
    id: 1,
    name: "Ana Silva",
    expertise: ["Captação", "Gestão"],
    lookingFor: "Investidores",
    offering: "Mentoria",
    verified: true
  }
];
```

**Funcionalidades**:
- Perfis de membros
- Eventos e oportunidades
- Sistema de filtros
- Conexões e matching

## Padrões de Desenvolvimento

### 1. Estrutura de Componentes
```typescript
// Padrão de componente funcional
const ComponentName = () => {
  const [state, setState] = useState(initialValue);
  
  useEffect(() => {
    // Side effects
  }, [dependencies]);
  
  return (
    <div className="container">
      {/* JSX */}
    </div>
  );
};

export default ComponentName;
```

### 2. Tipagem TypeScript
```typescript
// Interfaces para props
interface ComponentProps {
  title: string;
  optional?: boolean;
  children: React.ReactNode;
}

// Types para dados
type UserRole = "admin" | "user" | "mentor";

// Enums para constantes
enum AssessmentStatus {
  PENDING = "pending",
  COMPLETED = "completed",
  EXPIRED = "expired"
}
```

### 3. Gerenciamento de Estado
```typescript
// Estado local com useState
const [loading, setLoading] = useState(false);
const [data, setData] = useState<DataType[]>([]);

// Estado de formulário com React Hook Form
const form = useForm<FormData>({
  resolver: zodResolver(schema),
  defaultValues: {}
});
```

### 4. Styling com Tailwind
```typescript
// Classes utilitárias
className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm"

// Conditional classes
className={`base-classes ${condition ? 'active-classes' : 'inactive-classes'}`}

// Component variants com CVA
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground",
        outline: "border border-input"
      }
    }
  }
);
```

## Performance e Otimização

### 1. Code Splitting
- Lazy loading de páginas com React.lazy()
- Suspense boundaries para loading states
- Dynamic imports para componentes pesados

### 2. Otimização de Imagens
- Lazy loading de imagens
- Formatos otimizados (WebP)
- Responsive images com srcset

### 3. Bundle Optimization
- Tree shaking automático com Vite
- Minificação de CSS e JS
- Compression gzip/brotli

## Acessibilidade

### 1. Componentes Radix UI
- Navegação por teclado
- Screen reader support
- ARIA attributes automáticos

### 2. Padrões Implementados
- Contraste adequado de cores
- Focus indicators visíveis
- Semantic HTML
- Alt text para imagens

## Responsividade

### 1. Breakpoints Tailwind
```css
sm: 640px   /* Mobile landscape */
md: 768px   /* Tablet */
lg: 1024px  /* Desktop */
xl: 1280px  /* Large desktop */
2xl: 1536px /* Extra large */
```

### 2. Grid Responsivo
```typescript
className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
```

## Configuração de Build

### Vite Configuration
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
```

### TypeScript Configuration
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

## Deployment

### Build Process
```bash
# Desenvolvimento
npm run dev

# Build para produção
npm run build

# Preview do build
npm run preview

# Linting
npm run lint
```

### Environment Variables
```env
VITE_API_URL=https://api.atacpro.com
VITE_APP_VERSION=1.0.0
VITE_ANALYTICS_ID=GA_TRACKING_ID
```

---

**Versão**: 1.0.0  
**Última atualização**: Fevereiro 2024
