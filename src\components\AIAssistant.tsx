
import React, { useState } from "react";
import { Bot, X, PanelRightClose, PanelRightOpen, SendHorizontal, User, UserRound, Smile, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";

type Message = {
  content: string;
  role: "user" | "assistant";
};

type AIAssistantProps = {
  context?: "assessment" | "valuation" | "learning";
};

const AIAssistant = ({ context = "assessment" }: AIAssistantProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [messages, setMessages] = useState<Message[]>([
    {
      role: "assistant",
      content: 
        context === "assessment" 
          ? "Olá! Sou sua assistente virtual da ATAC PRO. Estou aqui para ajudar com o Assessment de Maturidade. Como posso ajudar você hoje?"
        : context === "valuation"
          ? "Olá! Sou sua assistente virtual da ATAC PRO. Estou aqui para ajudar com a Avaliação de Valuation. Como posso ajudar você hoje?"
        : "Olá! Sou sua assistente virtual da ATAC PRO. Estou aqui para ajudar com informações sobre nossas Experiências de Aprendizado Globais. Tem alguma dúvida sobre destinos, programação ou valores?"
    }
  ]);

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      // Add user message
      setMessages([...messages, { role: "user", content: inputValue }]);
      
      // Simulate AI response based on context
      setTimeout(() => {
        let response = "";
        
        // Simple response logic based on keywords
        const lowerCaseInput = inputValue.toLowerCase();
        
        if (context === "assessment") {
          if (lowerCaseInput.includes("solução") || lowerCaseInput.includes("solucao")) {
            response = "Na seção SOLUÇÃO, avaliamos a escalabilidade, o nível de inovação e o grau de validação da sua solução. Escolha a opção que melhor representa o estágio atual da sua startup.";
          } else if (lowerCaseInput.includes("mindset")) {
            response = "Na seção MINDSET, avaliamos o conhecimento do time sobre startups e inovação. Isso é importante para entender a mentalidade empreendedora da equipe.";
          } else if (lowerCaseInput.includes("equipe")) {
            response = "Na seção EQUIPE, analisamos o tamanho e a multidisciplinaridade da sua equipe. Startups com times diversos tendem a ter uma visão mais ampla dos problemas.";
          } else if (lowerCaseInput.includes("produto") || lowerCaseInput.includes("serviço") || lowerCaseInput.includes("servico")) {
            response = "Na seção PRODUTO/SERVIÇO, avaliamos o grau de validação da sua solução no mercado. É importante saber se seu produto já foi testado com clientes reais.";
          } else if (lowerCaseInput.includes("negócio") || lowerCaseInput.includes("negocio")) {
            response = "Na seção NEGÓCIOS, analisamos a validação do seu modelo de negócios. Um modelo bem validado é essencial para a sustentabilidade da startup.";
          } else if (lowerCaseInput.includes("pitch")) {
            response = "Na seção PITCH, avaliamos seu preparo para apresentar sua solução. Um bom pitch é crucial para atrair investidores e clientes.";
          } else if (lowerCaseInput.includes("gestão") || lowerCaseInput.includes("gestao") || lowerCaseInput.includes("projeto")) {
            response = "Na seção GESTÃO DE PROJETOS, analisamos o nível de conhecimento em gestão ágil, gestão de pessoas, metas, OKRs e dados na sua startup.";
          } else if (lowerCaseInput.includes("marketing") || lowerCaseInput.includes("venda")) {
            response = "Na seção MARKETING E VENDAS, avaliamos a maturidade da sua equipe comercial, presença digital e satisfação com o time.";
          } else if (lowerCaseInput.includes("jurídico") || lowerCaseInput.includes("juridico") || lowerCaseInput.includes("contábil") || lowerCaseInput.includes("contabil")) {
            response = "Na seção JURÍDICO E CONTÁBIL, analisamos a maturidade jurídica, proteção legal e conformidade com LGPD da sua startup.";
          } else {
            response = "O Assessment de Maturidade ajuda a entender o estágio atual da sua startup em diferentes áreas. Selecione a opção que melhor representa sua realidade em cada pergunta. Posso explicar alguma seção específica?";
          }
        } else if (context === "valuation") {
          response = "Para uma avaliação precisa de valuation, é importante fornecer informações precisas sobre receitas, custos, crescimento e mercado. Alguma dúvida específica sobre o preenchimento?";
        } else {
          // Learning context
          if (lowerCaseInput.includes("vale do silício") || lowerCaseInput.includes("vale do silicio")) {
            response = "Nossa experiência no Vale do Silício inclui visitas às sedes do Google, Apple e Meta, além de workshops exclusivos com empreendedores locais. O investimento é de R$ 18.900 por pessoa, com duração de 6 dias.";
          } else if (lowerCaseInput.includes("china") || lowerCaseInput.includes("fintech")) {
            response = "A Imersão China Fintech acontece de 12 a 19 de Janeiro de 2024, com visitas às sedes da Ant Group e Tencent. O investimento é de R$ 22.900 por pessoa, com duração de 7 dias.";
          } else if (lowerCaseInput.includes("ai") || lowerCaseInput.includes("ia") || lowerCaseInput.includes("inteligência artificial")) {
            response = "Nossa AI Learning Experience acontece de 5 a 10 de Novembro de 2023, com visita ao centro de pesquisa da OpenAI e encontro com fundadores de startups de IA. O investimento é de R$ 17.500 por pessoa.";
          } else if (lowerCaseInput.includes("preço") || lowerCaseInput.includes("preco") || lowerCaseInput.includes("valor") || lowerCaseInput.includes("investimento")) {
            response = "Nossos programas de imersão têm investimentos a partir de R$ 17.500 por pessoa, dependendo do destino e duração. Todos incluem hospedagem, visitas técnicas e workshops exclusivos.";
          } else if (lowerCaseInput.includes("duração") || lowerCaseInput.includes("duracao") || lowerCaseInput.includes("dias")) {
            response = "Nossas experiências de aprendizado globais têm duração entre 5 e 7 dias, dependendo do destino e da programação.";
          } else if (lowerCaseInput.includes("inclui") || lowerCaseInput.includes("incluso")) {
            response = "Nossos programas incluem hospedagem, café da manhã, traslados locais, visitas técnicas, workshops exclusivos e material didático. Não estão inclusos passagens aéreas internacionais e refeições não especificadas.";
          } else {
            response = "Oferecemos experiências imersivas em ecossistemas de inovação globais como Vale do Silício, China e centros de IA. Posso detalhar alguma experiência específica ou responder sobre valores, programação e datas?";
          }
        }
        
        setMessages(prev => [...prev, { role: "assistant", content: response }]);
      }, 1000);
      
      setInputValue("");
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Floating avatar button with animation */}
      {!isOpen && (
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={() => setIsOpen(true)}
            className="rounded-full h-10 w-10 bg-brand-secondary hover:bg-brand-dark shadow-md p-0 border border-white/20 transition-all duration-300 hover:scale-105"
            aria-label="Assistente virtual"
          >
            <div className="absolute inset-0 rounded-full bg-white/10 animate-ping" style={{ animationDuration: '3s' }}></div>
            <Sparkles className="h-5 w-5 text-white" />
          </Button>
        </div>
      )}

      {/* Drawer for mobile and smaller viewports */}
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerContent className="sm:max-w-md mx-auto h-[70vh]">
          <DrawerHeader className="border-b bg-brand-secondary text-white">
            <DrawerTitle className="flex items-center">
              <Sparkles className="mr-2 h-5 w-5" />
              Assistente ATAC
            </DrawerTitle>
            <DrawerDescription className="text-white/90">
              Estou aqui para ajudar com suas dúvidas.
            </DrawerDescription>
          </DrawerHeader>
          
          <div className="flex-1 overflow-auto p-4 space-y-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`flex ${
                  msg.role === "assistant" ? "justify-start" : "justify-end"
                }`}
              >
                {msg.role === "assistant" && (
                  <div className="h-8 w-8 rounded-full bg-brand-secondary flex items-center justify-center mr-2 flex-shrink-0">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    msg.role === "assistant"
                      ? "bg-gray-100 text-gray-800"
                      : "bg-brand-secondary text-white"
                  }`}
                >
                  {msg.content}
                </div>
                {msg.role === "user" && (
                  <div className="h-8 w-8 rounded-full bg-brand-secondary flex items-center justify-center ml-2 flex-shrink-0">
                    <UserRound className="h-5 w-5 text-white" />
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <DrawerFooter className="border-t">
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Digite sua pergunta..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1"
              />
              <Button onClick={handleSendMessage} size="icon" className="bg-brand-secondary text-white hover:bg-brand-dark">
                <SendHorizontal className="h-4 w-4" />
              </Button>
            </div>
            <DrawerClose asChild>
              <Button variant="outline">Fechar</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default AIAssistant;
