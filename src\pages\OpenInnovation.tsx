
import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Rocket, 
  Lightbulb, 
  Handshake, 
  Sparkles, 
  Building2, 
  Briefcase, 
  LineChart, 
  Target, 
  CheckCircle, 
  ArrowRight,
  Mic,
  Users,
  Globe,
  Award,
  Calendar,
  Play,
  Video,
  BarChart3
} from "lucide-react";

const OpenInnovation = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Open Innovation - ATAC Academy";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-16">
        {/* Hero Section */}
        <section className="py-24 bg-gradient-to-r from-brand to-brand-accent text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="font-montserrat text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                Open Innovation
              </h1>
              <p className="text-xl text-white/90 mb-10 max-w-3xl mx-auto">
                Conectamos corporações a startups para acelerar a inovação e criar vantagens competitivas sustentáveis através de modelos colaborativos.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link to="/contato">
                  <Button className="bg-white text-brand hover:bg-gray-100 px-8 py-6 text-lg">
                    Fale com um Consultor
                  </Button>
                </Link>
                <a href="#benefits">
                  <Button className="bg-white text-brand hover:bg-gray-100 px-8 py-6 text-lg">
                    Conheça os Benefícios
                  </Button>
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* What is Open Innovation */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  O que é <span className="gradient-text">Open Innovation</span>?
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  Open Innovation é um paradigma que propõe que as empresas podem e devem usar ideias externas, além de ideias internas, para avançar em sua tecnologia e soluções.
                </p>
                <p className="text-lg text-gray-700 mb-6">
                  É uma abordagem colaborativa que permite que corporações se conectem com startups, universidades e centros de pesquisa para desenvolver novos produtos, serviços e modelos de negócio.
                </p>
                <p className="text-lg text-gray-700">
                  A ATAC Academy atua como facilitadora desse processo, criando programas sob medida que conectam corporações às startups mais promissoras e relevantes para seus desafios de negócio.
                </p>
              </div>
              <div className="md:w-1/2">
                <div className="rounded-xl overflow-hidden shadow-xl">
                  <img 
                    src="https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" 
                    alt="Equipe de inovação colaborando" 
                    className="w-full h-auto"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Impact Numbers Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Nosso <span className="gradient-text">Impacto</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Transformando o ecossistema de inovação com resultados concretos
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-white p-8 rounded-xl shadow-md text-center transform transition-all duration-300 hover:scale-105">
                <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mx-auto mb-6">
                  <Building2 className="h-8 w-8 text-brand" />
                </div>
                <h3 className="text-4xl font-bold mb-2 text-gray-900">120+</h3>
                <p className="text-gray-600">Empresas atendidas</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-md text-center transform transition-all duration-300 hover:scale-105">
                <div className="w-16 h-16 bg-brand-accent/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Users className="h-8 w-8 text-brand-accent" />
                </div>
                <h3 className="text-4xl font-bold mb-2 text-gray-900">5.000+</h3>
                <p className="text-gray-600">Executivos capacitados</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-md text-center transform transition-all duration-300 hover:scale-105">
                <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mx-auto mb-6">
                  <Rocket className="h-8 w-8 text-brand" />
                </div>
                <h3 className="text-4xl font-bold mb-2 text-gray-900">350+</h3>
                <p className="text-gray-600">Startups aceleradas</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-md text-center transform transition-all duration-300 hover:scale-105">
                <div className="w-16 h-16 bg-brand-accent/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Globe className="h-8 w-8 text-brand-accent" />
                </div>
                <h3 className="text-4xl font-bold mb-2 text-gray-900">15</h3>
                <p className="text-gray-600">Países com atividades</p>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits */}
        <section id="benefits" className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Benefícios da <span className="gradient-text">Open Innovation</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Descubra como nossa abordagem de inovação aberta pode transformar sua empresa.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="feature-box">
                <div className="w-14 h-14 bg-brand-light rounded-full flex items-center justify-center mb-6">
                  <Rocket className="h-7 w-7 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-3">Aceleração da Inovação</h3>
                <p className="text-gray-600">
                  Reduza o tempo de desenvolvimento de novas soluções ao colaborar com startups que já possuem tecnologias validadas.
                </p>
              </div>

              <div className="feature-box">
                <div className="w-14 h-14 bg-brand-light rounded-full flex items-center justify-center mb-6">
                  <Lightbulb className="h-7 w-7 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-3">Novas Perspectivas</h3>
                <p className="text-gray-600">
                  Acesse ideias frescas e abordagens disruptivas que podem transformar seu modelo de negócio e suas ofertas ao mercado.
                </p>
              </div>

              <div className="feature-box">
                <div className="w-14 h-14 bg-brand-light rounded-full flex items-center justify-center mb-6">
                  <LineChart className="h-7 w-7 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-3">Redução de Riscos</h3>
                <p className="text-gray-600">
                  Minimize riscos de investimento ao testar e validar conceitos com startups antes de grandes implementações internas.
                </p>
              </div>

              <div className="feature-box">
                <div className="w-14 h-14 bg-brand-light rounded-full flex items-center justify-center mb-6">
                  <Handshake className="h-7 w-7 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-3">Ecossistema Colaborativo</h3>
                <p className="text-gray-600">
                  Estabeleça parcerias estratégicas com startups, universidades e centros de pesquisa para um fluxo contínuo de inovação.
                </p>
              </div>

              <div className="feature-box">
                <div className="w-14 h-14 bg-brand-light rounded-full flex items-center justify-center mb-6">
                  <Target className="h-7 w-7 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-3">Foco no Core Business</h3>
                <p className="text-gray-600">
                  Mantenha sua equipe focada nas competências essenciais enquanto startups especialistas desenvolvem soluções complementares.
                </p>
              </div>

              <div className="feature-box">
                <div className="w-14 h-14 bg-brand-light rounded-full flex items-center justify-center mb-6">
                  <Sparkles className="h-7 w-7 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-3">Cultura de Inovação</h3>
                <p className="text-gray-600">
                  Estimule uma mentalidade inovadora em toda a organização ao expor suas equipes a novas formas de pensar e trabalhar.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Clients Section */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Empresas que Confiam em <span className="gradient-text">Nós</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Parceiros estratégicos em diferentes setores e indústrias
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/b4470602-f52e-4e23-a35b-96d4f1d10cae.png" alt="Company Logo 1" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/5c96ac48-de8a-4c35-938b-4512bbd9e0da.png" alt="Company Logo 2" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/4399656e-a62f-4505-99f7-e31d8db79e90.png" alt="Company Logo 3" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/404e5514-d5d1-42e3-aec7-7afe0659a109.png" alt="Company Logo 4" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/df9529ed-ecac-4ab6-9e49-549a6c8ef697.png" alt="Company Logo 5" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/d7ee6582-3626-4fef-ab16-e533794ce84f.png" alt="Company Logo 6" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/84acc76f-7f0c-4a07-9355-2e604b347140.png" alt="Company Logo 7" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/3f096ed9-1497-4692-909e-d2d529b33d3e.png" alt="Company Logo 8" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/b5c971b0-e98b-4117-b372-0b46ff1ce95b.png" alt="Company Logo 9" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/15506a58-7a8d-486b-95f4-894b991ed85b.png" alt="Company Logo 10" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/d2cd3af4-591e-4991-b1b0-054a49a508d1.png" alt="Company Logo 11" className="max-h-16" />
              </div>
              <div className="flex justify-center items-center p-4 grayscale hover:grayscale-0 transition-all duration-300">
                <img src="/lovable-uploads/9f1d2520-fa14-40df-9615-72abd62459bc.png" alt="Company Logo 12" className="max-h-16" />
              </div>
            </div>
          </div>
        </section>

        {/* Media Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                <span className="gradient-text">Mídia</span> & Conteúdo
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Fique por dentro dos nossos eventos, podcasts e últimas notícias
              </p>
            </div>
            
            <Tabs defaultValue="podcasts" className="w-full max-w-5xl mx-auto">
              <TabsList className="grid w-full grid-cols-3 mb-10">
                <TabsTrigger value="podcasts">Podcasts</TabsTrigger>
                <TabsTrigger value="eventos">Eventos</TabsTrigger>
                <TabsTrigger value="noticias">Notícias</TabsTrigger>
              </TabsList>
              
              <TabsContent value="podcasts" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {/* Podcast 1 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="relative">
                      <img 
                        src="https://images.unsplash.com/photo-1614680376739-414d95ff43df?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                        alt="Podcast" 
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <div className="w-14 h-14 bg-brand/80 rounded-full flex items-center justify-center cursor-pointer hover:bg-brand transition-all">
                          <Play className="h-6 w-6 text-white ml-1" />
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-3">
                        <Mic className="h-4 w-4 text-brand mr-2" />
                        <span className="text-sm text-brand">Innovation Talks</span>
                      </div>
                      <h3 className="text-xl font-bold mb-2">O futuro da inovação corporativa</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        Entrevista com CEOs de grandes empresas sobre estratégias de inovação aberta e resultados obtidos.
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500 text-sm">45 min</span>
                        <Button variant="ghost" size="sm" className="text-brand hover:text-brand-secondary">
                          Ouvir agora
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Podcast 2 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="relative">
                      <img 
                        src="https://images.unsplash.com/photo-1519222970733-f546218fa6d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                        alt="Podcast" 
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <div className="w-14 h-14 bg-brand/80 rounded-full flex items-center justify-center cursor-pointer hover:bg-brand transition-all">
                          <Play className="h-6 w-6 text-white ml-1" />
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-3">
                        <Mic className="h-4 w-4 text-brand mr-2" />
                        <span className="text-sm text-brand">Tech Trends</span>
                      </div>
                      <h3 className="text-xl font-bold mb-2">Inteligência Artificial na prática</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        Como grandes empresas estão implementando IA em seus processos e produtos.
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500 text-sm">38 min</span>
                        <Button variant="ghost" size="sm" className="text-brand hover:text-brand-secondary">
                          Ouvir agora
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Podcast 3 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                    <div className="relative">
                      <img 
                        src="https://images.unsplash.com/photo-1589903308904-1010c2294adc?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                        alt="Podcast" 
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <div className="w-14 h-14 bg-brand/80 rounded-full flex items-center justify-center cursor-pointer hover:bg-brand transition-all">
                          <Play className="h-6 w-6 text-white ml-1" />
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-3">
                        <Mic className="h-4 w-4 text-brand mr-2" />
                        <span className="text-sm text-brand">Startup Connect</span>
                      </div>
                      <h3 className="text-xl font-bold mb-2">Da ideia ao mercado: cases de sucesso</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        Startups que revolucionaram setores tradicionais através de parcerias corporativas.
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500 text-sm">52 min</span>
                        <Button variant="ghost" size="sm" className="text-brand hover:text-brand-secondary">
                          Ouvir agora
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="text-center mt-10">
                  <Button variant="outline" className="border-brand text-brand hover:bg-brand/10">
                    Ver todos os episódios
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </TabsContent>
              
              <TabsContent value="eventos" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Evento 1 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col md:flex-row">
                    <div className="md:w-1/3 relative">
                      <img 
                        src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                        alt="Evento Innovation Summit" 
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-4 left-4 bg-brand text-white text-xs font-medium py-1 px-2 rounded">
                        Presencial
                      </div>
                    </div>
                    <div className="p-6 md:w-2/3">
                      <div className="flex items-center mb-3">
                        <Calendar className="h-4 w-4 text-brand mr-2" />
                        <span className="text-sm text-brand">25 de Outubro, 2023</span>
                      </div>
                      <h3 className="text-xl font-bold mb-2">Innovation Summit 2023</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        Conferência anual reunindo líderes de inovação para discutir tendências e cases de sucesso em São Paulo.
                      </p>
                      <Button className="w-full bg-brand hover:bg-brand-secondary">
                        Inscrever-se
                      </Button>
                    </div>
                  </div>
                  
                  {/* Evento 2 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col md:flex-row">
                    <div className="md:w-1/3 relative">
                      <img 
                        src="https://images.unsplash.com/photo-1543269865-cbf427effbad?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                        alt="Workshop de Design Thinking" 
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-4 left-4 bg-brand-accent text-white text-xs font-medium py-1 px-2 rounded">
                        Online
                      </div>
                    </div>
                    <div className="p-6 md:w-2/3">
                      <div className="flex items-center mb-3">
                        <Calendar className="h-4 w-4 text-brand mr-2" />
                        <span className="text-sm text-brand">15 de Novembro, 2023</span>
                      </div>
                      <h3 className="text-xl font-bold mb-2">Workshop de Design Thinking</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        Sessão prática para executivos sobre como aplicar metodologias ágeis na resolução de desafios corporativos.
                      </p>
                      <Button className="w-full bg-brand hover:bg-brand-secondary">
                        Inscrever-se
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="text-center mt-10">
                  <Button variant="outline" className="border-brand text-brand hover:bg-brand/10">
                    Ver agenda completa
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </TabsContent>
              
              <TabsContent value="noticias" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {/* Notícia 1 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                    <img 
                      src="https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                      alt="Notícia" 
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-6">
                      <span className="text-xs text-gray-500">12 de Setembro, 2023</span>
                      <h3 className="text-lg font-bold mt-2 mb-3">ATAC Academy lança novo programa de aceleração para fintechs</h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        O programa conectará startups do setor financeiro com os principais bancos e instituições financeiras do país.
                      </p>
                      <Button variant="link" className="text-brand p-0 h-auto">
                        Ler mais
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Notícia 2 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                    <img 
                      src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                      alt="Notícia" 
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-6">
                      <span className="text-xs text-gray-500">28 de Agosto, 2023</span>
                      <h3 className="text-lg font-bold mt-2 mb-3">Estudo revela impacto da Open Innovation no Brasil</h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        Pesquisa conduzida pela ATAC Academy mostra que empresas que investem em inovação aberta crescem 2x mais rápido.
                      </p>
                      <Button variant="link" className="text-brand p-0 h-auto">
                        Ler mais
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Notícia 3 */}
                  <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300">
                    <img 
                      src="https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                      alt="Notícia" 
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-6">
                      <span className="text-xs text-gray-500">15 de Agosto, 2023</span>
                      <h3 className="text-lg font-bold mt-2 mb-3">ATAC Academy expande operações para América Latina</h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        Com escritórios no México e Colômbia, a empresa reforça seu compromisso com o ecossistema de inovação latino-americano.
                      </p>
                      <Button variant="link" className="text-brand p-0 h-auto">
                        Ler mais
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="text-center mt-10">
                  <Button variant="outline" className="border-brand text-brand hover:bg-brand/10">
                    Ver todas as notícias
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Success Stories */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Casos de <span className="gradient-text">Sucesso</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Conheça empresas que transformaram seus negócios através da Open Innovation
              </p>
            </div>
            
            {/* Add your success stories content here */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Success Story 1 */}
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="p-8">
                  <h3 className="text-2xl font-bold mb-4">Banco Digital + Fintech</h3>
                  <p className="text-gray-700 mb-6">
                    Uma colaboração que resultou em um novo produto financeiro, aumentando a base de clientes em 30% e reduzindo custos operacionais em 25%.
                  </p>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-600">Implementação em apenas 4 meses</span>
                  </div>
                  <div className="flex items-center mt-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-600">ROI de 300% no primeiro ano</span>
                  </div>
                </div>
              </div>
              
              {/* Success Story 2 */}
              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="p-8">
                  <h3 className="text-2xl font-bold mb-4">Indústria + Startup de IoT</h3>
                  <p className="text-gray-700 mb-6">
                    Implementação de soluções de monitoramento em tempo real que aumentaram a eficiência produtiva em 40% e reduziram o desperdício em 28%.
                  </p>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-600">Economia anual de R$ 2 milhões</span>
                  </div>
                  <div className="flex items-center mt-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-600">Adoção em todas as unidades em 6 meses</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="text-center mt-12">
              <Link to="/corporate-challenges">
                <Button className="bg-brand hover:bg-brand-secondary text-white">
                  Conheça nossos Corporate Challenges
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
        
        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-brand to-brand-accent text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
                Pronto para acelerar a inovação na sua empresa?
              </h2>
              <p className="text-xl text-white/90 mb-10 max-w-3xl mx-auto">
                Entre em contato com nossos consultores e descubra como a ATAC PRO pode ajudar sua empresa a se conectar com as melhores startups do mercado.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link to="/contato">
                  <Button className="bg-white text-brand hover:bg-gray-100 px-8 py-6 text-lg">
                    Fale com um Consultor
                  </Button>
                </Link>
                <Link to="/corporate-challenges">
                  <Button className="bg-brand-secondary text-white hover:bg-brand-secondary/90 px-8 py-6 text-lg border border-white/30">
                    Corporate Challenges
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default OpenInnovation;
