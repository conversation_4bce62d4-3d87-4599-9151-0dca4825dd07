
import React, { useState, useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { 
  Play, Pause, Share2, Clock, Heart, ListMusic, 
  ChevronRight, ChevronLeft, Headphones, PlusCircle, 
  Mic, Timer, Volume2, Facebook, Twitter, Instagram
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Link } from "react-router-dom";

interface PodcastEpisode {
  id: number;
  title: string;
  description: string;
  date: string;
  duration: string;
  imageUrl: string;
  isPlaying?: boolean;
  isNew?: boolean;
}

interface PodcastShow {
  id: number;
  title: string;
  host: string;
  imageUrl: string;
  episodes: number;
  category: string;
}

const Podcast = () => {
  const [currentEpisode, setCurrentEpisode] = useState<PodcastEpisode | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState([0]);
  const [volume, setVolume] = useState([70]);

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "ATAC Prodcast | Insights de Inovação e Empreendedorismo";
  }, []);

  const togglePlay = (episode?: PodcastEpisode) => {
    if (episode) {
      setCurrentEpisode(episode);
      setIsPlaying(true);
    } else {
      setIsPlaying(!isPlaying);
    }
  };

  const featuredEpisodes: PodcastEpisode[] = [
    {
      id: 1,
      title: "Como captar investimento para startups em 2024",
      description: "Neste episódio, conversamos com investidores anjo e VCs sobre as tendências de investimento para startups em 2024.",
      date: "15 Jun 2024",
      duration: "45:18",
      imageUrl: "https://images.unsplash.com/photo-1589903308904-1010c2294adc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      isNew: true
    },
    {
      id: 2,
      title: "Inteligência Artificial na Gestão de Negócios",
      description: "Especialistas discutem como a IA está transformando a maneira como administramos empresas e tomamos decisões estratégicas.",
      date: "8 Jun 2024",
      duration: "52:45",
      imageUrl: "https://images.unsplash.com/photo-1677442135743-d6c10b324872?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80"
    },
    {
      id: 3,
      title: "O futuro das startups de blockchain no Brasil",
      description: "Um panorama sobre o mercado de blockchain e criptoativos no Brasil, com foco em oportunidades para empreendedores.",
      date: "1 Jun 2024",
      duration: "38:22",
      imageUrl: "https://images.unsplash.com/photo-1639762681057-408e52192e55?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80"
    },
    {
      id: 4,
      title: "Estratégias de Growth para Startups B2B",
      description: "Conheça as melhores práticas de crescimento para startups que atuam no mercado B2B.",
      date: "25 Mai 2024",
      duration: "41:53",
      imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
  ];

  const podcastShows: PodcastShow[] = [
    {
      id: 1,
      title: "ATAC Insights",
      host: "Maria Silva",
      imageUrl: "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      episodes: 24,
      category: "Empreendedorismo"
    },
    {
      id: 2,
      title: "Tech Talks",
      host: "Pedro Almeida",
      imageUrl: "https://images.unsplash.com/photo-1550063873-ab792950096b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      episodes: 18,
      category: "Tecnologia"
    },
    {
      id: 3,
      title: "Venture Capital Hour",
      host: "Ana Costa & João Santos",
      imageUrl: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      episodes: 12,
      category: "Investimentos"
    },
  ];

  const moreEpisodes: PodcastEpisode[] = [
    {
      id: 5,
      title: "Product Market Fit: Como validar seu produto no mercado",
      description: "Descubra as estratégias para encontrar o encaixe perfeito entre seu produto e o mercado.",
      date: "18 Mai 2024",
      duration: "36:45",
      imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: 6,
      title: "Liderança em tempos de crise: Aprendizados pós-pandemia",
      description: "Líderes de grandes empresas compartilham suas experiências e aprendizados sobre gestão durante e após a pandemia.",
      date: "11 Mai 2024",
      duration: "48:12",
      imageUrl: "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1784&q=80"
    },
    {
      id: 7,
      title: "Sucessos e fracassos: Histórias de empreendedores brasileiros",
      description: "Empreendedores compartilham suas jornadas de altos e baixos e como transformaram fracassos em aprendizados valiosos.",
      date: "4 Mai 2024",
      duration: "55:38",
      imageUrl: "https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: 8,
      title: "Marketing digital para startups com orçamento limitado",
      description: "Estratégias eficientes de marketing digital para startups em estágios iniciais e com recursos limitados.",
      date: "27 Abr 2024",
      duration: "42:17",
      imageUrl: "https://images.unsplash.com/photo-1568992687947-868a62a9f521?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80"
    },
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <Navbar />
      
      <main className="flex-grow pt-24 pb-32">
        <div className="bg-gradient-to-r from-brand to-brand-secondary text-white py-10">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-6 md:mb-0">
                <h1 className="text-4xl md:text-5xl font-bold mb-3 font-montserrat">ATAC Prodcast</h1>
                <p className="text-xl opacity-90 max-w-xl">
                  Insights sobre inovação, empreendedorismo e o ecossistema de startups
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Button className="bg-white text-brand hover:bg-gray-100">
                  <PlusCircle size={18} className="mr-2" />
                  Inscrever-se
                </Button>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" className="bg-white/20 hover:bg-white/30 text-white">
                    <Facebook size={20} />
                  </Button>
                  <Button variant="ghost" size="icon" className="bg-white/20 hover:bg-white/30 text-white">
                    <Twitter size={20} />
                  </Button>
                  <Button variant="ghost" size="icon" className="bg-white/20 hover:bg-white/30 text-white">
                    <Instagram size={20} />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 mt-8">
          <div className="mb-12">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Episódios em Destaque</h2>
              <Button variant="ghost" className="text-brand">
                Ver todos <ChevronRight size={18} />
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredEpisodes.map((episode) => (
                <Card key={episode.id} className="overflow-hidden group hover:shadow-md transition-all">
                  <div className="relative">
                    <div className="aspect-square overflow-hidden">
                      <img 
                        src={episode.imageUrl}
                        alt={episode.title}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button 
                        onClick={() => togglePlay(episode)}
                        className="bg-brand-accent hover:bg-brand-accent/90 text-white rounded-full w-14 h-14 flex items-center justify-center"
                      >
                        <Play size={24} fill="white" className="ml-1" />
                      </Button>
                    </div>
                    {episode.isNew && (
                      <div className="absolute top-3 right-3 bg-brand-accent text-white text-xs font-bold py-1 px-2 rounded">
                        NOVO
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-bold text-gray-900 mb-1 line-clamp-1 group-hover:text-brand transition-colors">
                      {episode.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {episode.description}
                    </p>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span>{episode.date}</span>
                      <div className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        <span>{episode.duration}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="mb-12 bg-white rounded-xl p-6 shadow-sm">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Nossos Shows</h2>
              <Button variant="ghost" className="text-brand">
                Ver todos <ChevronRight size={18} />
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {podcastShows.map((show) => (
                <div key={show.id} className="group flex rounded-lg overflow-hidden border hover:shadow-md transition-all">
                  <div className="w-24 h-24 flex-shrink-0">
                    <img 
                      src={show.imageUrl}
                      alt={show.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-grow p-4">
                    <h3 className="font-bold text-gray-900 mb-1 group-hover:text-brand transition-colors">
                      {show.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-1">
                      com {show.host}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{show.episodes} episódios</span>
                      <span className="bg-gray-100 px-2 py-1 rounded-full">
                        {show.category}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-12">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Mais Episódios</h2>
              <div className="flex gap-2">
                <Button variant="outline" size="icon" className="text-gray-600">
                  <ChevronLeft size={18} />
                </Button>
                <Button variant="outline" size="icon" className="text-gray-600">
                  <ChevronRight size={18} />
                </Button>
              </div>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="grid grid-cols-[auto_1fr_auto] text-sm font-medium text-gray-500 px-6 py-3 border-b">
                <div className="w-14 text-center">#</div>
                <div>TÍTULO</div>
                <div className="w-24 text-center flex items-center justify-end">
                  <Clock size={16} />
                </div>
              </div>
              
              {moreEpisodes.map((episode, index) => (
                <div 
                  key={episode.id}
                  className="grid grid-cols-[auto_1fr_auto] items-center px-6 py-4 hover:bg-gray-50 border-b last:border-b-0 group"
                >
                  <div className="w-14 text-center flex justify-center">
                    <div className="group-hover:hidden text-gray-500 font-medium">
                      {index + 1}
                    </div>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="hidden group-hover:flex text-brand"
                      onClick={() => togglePlay(episode)}
                    >
                      <Play size={20} />
                    </Button>
                  </div>
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded overflow-hidden mr-4">
                      <img 
                        src={episode.imageUrl}
                        alt={episode.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 group-hover:text-brand transition-colors">
                        {episode.title}
                      </h3>
                      <p className="text-sm text-gray-500">{episode.date}</p>
                    </div>
                  </div>
                  <div className="w-24 flex items-center justify-end gap-3 text-gray-500">
                    <Button variant="ghost" size="icon" className="hidden group-hover:flex">
                      <Heart size={18} />
                    </Button>
                    <span>{episode.duration}</span>
                  </div>
                </div>
              ))}
              
              <div className="flex justify-center py-6">
                <Button className="bg-brand hover:bg-brand-secondary text-white">
                  Carregar mais episódios
                </Button>
              </div>
            </div>
          </div>

          <div className="rounded-xl overflow-hidden bg-gradient-to-r from-brand-dark to-brand-secondary text-white p-8">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center gap-3 mb-3">
                  <Headphones size={28} className="text-brand-accent" />
                  <h2 className="text-2xl font-bold">Não perca nenhum episódio</h2>
                </div>
                <p className="opacity-90 max-w-xl">
                  Inscreva-se no nosso podcast para receber notificações sobre novos episódios. 
                  Disponível no Spotify, Apple Podcasts, Google Podcasts e outras plataformas.
                </p>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button className="bg-white text-brand hover:bg-gray-100">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/1/19/Spotify_logo_without_text.svg" className="w-5 h-5 mr-2" alt="Spotify" />
                  Spotify
                </Button>
                <Button className="bg-white text-brand hover:bg-gray-100">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/e/e7/Podcasts_%28iOS%29.svg" className="w-5 h-5 mr-2" alt="Apple Podcasts" />
                  Apple Podcasts
                </Button>
                <Button className="bg-white text-brand hover:bg-gray-100">
                  <img src="https://upload.wikimedia.org/wikipedia/commons/c/c7/Google_Podcasts_logo.svg" className="w-5 h-5 mr-2" alt="Google Podcasts" />
                  Google Podcasts
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Podcast;
