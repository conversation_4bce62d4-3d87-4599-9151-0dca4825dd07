
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Check, ArrowRight, CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

const InscricaoPrograma = () => {
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Inscrição no Programa | ATAC PRO";
  }, []);

  const handleSubmitClick = (e) => {
    e.preventDefault();
    setShowPaymentDialog(true);
  };

  const handlePaymentCompletion = () => {
    setShowPaymentDialog(false);
    toast({
      title: "Pagamento concluído!",
      description: "Bem-vindo ao programa Acelere seu Negócio. Redirecionando para o login.",
      variant: "default",
    });
    
    // Redirect to login page after payment
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <section className="py-20 md:py-32 bg-gradient-to-br from-brand to-brand-accent text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="font-montserrat text-4xl md:text-6xl font-bold mb-6">
              Inscrição no Programa Acelere seu Negócio
            </h1>
            <p className="text-xl opacity-90 mb-10">
              Estamos quase lá! Complete o formulário abaixo para garantir sua vaga no programa.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card className="p-8 shadow-lg">
                <h2 className="text-2xl font-bold mb-6">Dados da Inscrição</h2>
                
                <form className="space-y-6" onSubmit={handleSubmitClick}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="font-medium">Nome Completo</label>
                      <input 
                        type="text" 
                        id="name" 
                        className="w-full p-3 border border-gray-300 rounded-md"
                        placeholder="Seu nome completo"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="font-medium">Email</label>
                      <input 
                        type="email" 
                        id="email" 
                        className="w-full p-3 border border-gray-300 rounded-md"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="phone" className="font-medium">Telefone</label>
                      <input 
                        type="tel" 
                        id="phone" 
                        className="w-full p-3 border border-gray-300 rounded-md"
                        placeholder="(00) 00000-0000"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="company" className="font-medium">Nome da Empresa/Startup</label>
                      <input 
                        type="text" 
                        id="company" 
                        className="w-full p-3 border border-gray-300 rounded-md"
                        placeholder="Nome da sua empresa"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="stage" className="font-medium">Estágio da Empresa</label>
                    <select 
                      id="stage" 
                      className="w-full p-3 border border-gray-300 rounded-md"
                    >
                      <option value="">Selecione o estágio</option>
                      <option value="ideacao">Ideação</option>
                      <option value="mvp">MVP</option>
                      <option value="validacao">Validação</option>
                      <option value="crescimento">Crescimento</option>
                      <option value="escala">Escala</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="about" className="font-medium">Conte sobre seu negócio</label>
                    <textarea 
                      id="about" 
                      rows={4}
                      className="w-full p-3 border border-gray-300 rounded-md"
                      placeholder="Descreva brevemente seu negócio, desafios e objetivos..."
                    ></textarea>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="referral" className="font-medium">Como conheceu o programa?</label>
                    <select 
                      id="referral" 
                      className="w-full p-3 border border-gray-300 rounded-md"
                    >
                      <option value="">Selecione uma opção</option>
                      <option value="social">Redes Sociais</option>
                      <option value="search">Busca Google</option>
                      <option value="indication">Indicação</option>
                      <option value="event">Evento</option>
                      <option value="other">Outro</option>
                    </select>
                  </div>

                  <div className="pt-4">
                    <Button type="submit" className="w-full bg-brand hover:bg-brand-secondary text-white px-8 py-6 text-lg font-medium">
                      Enviar Inscrição
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </form>
              </Card>
            </div>

            <div>
              <Card className="p-8 shadow-lg border-t-4 border-brand">
                <h3 className="text-xl font-bold mb-6">Resumo do Programa</h3>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-start">
                    <Check className="h-5 w-5 text-brand mt-1 mr-3 flex-shrink-0" />
                    <p>Programa completo de 3 meses</p>
                  </div>
                  <div className="flex items-start">
                    <Check className="h-5 w-5 text-brand mt-1 mr-3 flex-shrink-0" />
                    <p>24 aulas online com especialistas</p>
                  </div>
                  <div className="flex items-start">
                    <Check className="h-5 w-5 text-brand mt-1 mr-3 flex-shrink-0" />
                    <p>12 workshops práticos</p>
                  </div>
                  <div className="flex items-start">
                    <Check className="h-5 w-5 text-brand mt-1 mr-3 flex-shrink-0" />
                    <p>6 sessões de mentoria individual</p>
                  </div>
                  <div className="flex items-start">
                    <Check className="h-5 w-5 text-brand mt-1 mr-3 flex-shrink-0" />
                    <p>Acesso vitalício à comunidade</p>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6 mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">Investimento:</span>
                    <span className="font-bold text-xl">R$ 15.000</span>
                  </div>
                  <p className="text-sm text-gray-600">Por empresa (até 3 participantes)</p>
                </div>

                <div className="bg-gray-100 p-4 rounded-lg text-sm">
                  <p className="font-medium mb-2">Próxima turma:</p>
                  <p>Início em 15 de Agosto de 2023</p>
                  <p className="mt-2 text-brand-accent font-medium">Vagas limitadas!</p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Finalizar Pagamento</DialogTitle>
            <DialogDescription>
              Complete o pagamento para ter acesso ao programa Acelere seu Negócio.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">Programa Acelere seu Negócio</h3>
              <p className="text-sm text-gray-500 mb-2">Programa executivo online e ao vivo</p>
              <div className="flex justify-between items-center">
                <span className="font-semibold">R$ 15.000</span>
                <span className="text-xs text-green-600">ou 12x de R$ 1.250</span>
              </div>
            </div>
            <div className="grid gap-2">
              <label htmlFor="cardNumber" className="text-sm font-medium">Número do Cartão</label>
              <input 
                id="cardNumber" 
                type="text" 
                placeholder="0000 0000 0000 0000"
                className="border rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label htmlFor="expiry" className="text-sm font-medium">Validade</label>
                <input 
                  id="expiry" 
                  type="text" 
                  placeholder="MM/AA"
                  className="border rounded-md px-3 py-2 text-sm"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="cvc" className="text-sm font-medium">CVC</label>
                <input 
                  id="cvc" 
                  type="text" 
                  placeholder="123"
                  className="border rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPaymentDialog(false)}>Cancelar</Button>
            <Button className="bg-brand hover:bg-brand-secondary" onClick={handlePaymentCompletion}>
              <CreditCard className="mr-2 h-4 w-4" />
              Finalizar Pagamento
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <Footer />
    </div>
  );
};

export default InscricaoPrograma;
