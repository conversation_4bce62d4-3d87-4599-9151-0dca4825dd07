import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, ArrowRight, CheckCircle, Info } from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import AIAssistant from "@/components/AIAssistant";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";

// Import assessment questions
import { assessmentQuestions } from "@/data/assessmentQuestions";

// Generate form schema dynamically based on all questions
const generateFormSchema = () => {
  const schemaMap: Record<string, z.ZodString> = {};
  
  assessmentQuestions.forEach((question) => {
    const fieldName = `${question.category}-${question.id}`;
    schemaMap[fieldName] = z.string();
  });
  
  return z.object(schemaMap);
};

const formSchema = generateFormSchema();

const MaturityAssessment = () => {
  const [currentCategory, setCurrentCategory] = useState<string>("solution");
  const [progress, setProgress] = useState<number>(0);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {}
  });
  
  // Calculate progress whenever form values change
  useEffect(() => {
    const values = form.getValues();
    const totalQuestions = Object.keys(formSchema.shape).length;
    const answeredQuestions = Object.values(values).filter(Boolean).length;
    setProgress(Math.round((answeredQuestions / totalQuestions) * 100));
  }, [form.watch()]);
  
  // Set page title and scroll to top on load
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Assessment de Maturidade | ATAC Academy";
  }, []);
  
  // Define all categories with their display names
  const categories = {
    "solution": "SOLUÇÃO",
    "mindset": "MINDSET",
    "team": "EQUIPE",
    "product": "PRODUTO / SERVIÇO",
    "business": "NEGÓCIOS",
    "project": "GESTÃO DE PROJETOS",
    "marketing": "MARKETING E VENDAS",
    "legal": "JURÍDICO E CONTÁBIL",
    "pitch": "PITCH",
    "networking": "NETWORKING E ECOSSISTEMA"
  };
  
  // Navigate to different category
  const navigateToCategory = (category: string) => {
    setCurrentCategory(category);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Calculate next and previous categories
  const getCategoryIndex = () => {
    return Object.keys(categories).indexOf(currentCategory);
  };
  
  const getNextCategory = () => {
    const keys = Object.keys(categories);
    const currentIndex = getCategoryIndex();
    return currentIndex < keys.length - 1 ? keys[currentIndex + 1] : null;
  };
  
  const getPreviousCategory = () => {
    const keys = Object.keys(categories);
    const currentIndex = getCategoryIndex();
    return currentIndex > 0 ? keys[currentIndex - 1] : null;
  };
  
  // Handle form submission
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log("Assessment data:", data);
    
    toast({
      title: "Diagnóstico Concluído!",
      description: "Enviamos seu resultado por email. Em breve nossa equipe entrará em contato.",
    });
    
    setTimeout(() => {
      navigate("/diagnostic");
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-10 text-center">
            <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Assessment de <span className="text-brand-accent">Maturidade</span>
            </h1>
            <p className="text-lg text-gray-700 mb-6">
              Avalie o estágio atual da sua startup e receba recomendações personalizadas para crescer.
            </p>
            
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2 mt-8">
              <div 
                className="bg-brand-accent h-2.5 rounded-full transition-all duration-300" 
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500">{progress}% completo</p>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Category tabs */}
              <div className="flex flex-wrap gap-2 mb-8 justify-center">
                {Object.entries(categories).map(([key, label]) => (
                  <Button
                    key={key}
                    type="button"
                    variant={currentCategory === key ? "default" : "outline"}
                    className={`rounded-full text-sm ${currentCategory === key ? "bg-brand-accent hover:bg-brand-accent/90" : ""}`}
                    onClick={() => navigateToCategory(key)}
                  >
                    {label}
                  </Button>
                ))}
              </div>
              
              {/* Current category title */}
              <div className="bg-brand-accent/10 p-4 rounded-lg mb-8">
                <h2 className="font-montserrat text-xl font-semibold text-gray-900">
                  {categories[currentCategory as keyof typeof categories]}
                </h2>
              </div>
              
              {/* Render questions for current category */}
              <div className="space-y-10">
                {assessmentQuestions
                  .filter(q => q.category === currentCategory)
                  .map((question, index) => (
                    <FormField
                      key={question.id}
                      control={form.control}
                      name={`${question.category}-${question.id}` as any}
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-base font-semibold flex items-start gap-2">
                            {index + 1} - {question.question}
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Info className="h-4 w-4 text-gray-500" />
                                </TooltipTrigger>
                                <TooltipContent className="max-w-xs">
                                  <p>Escolha a opção que melhor representa o estágio atual da sua startup.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormLabel>
                          <FormControl>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Selecione uma opção" />
                              </SelectTrigger>
                              <SelectContent>
                                {question.options.map((option, i) => (
                                  <SelectItem key={i} value={String(i)}>
                                    {option}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ))
                }
              </div>
              
              {/* Navigation buttons */}
              <div className="flex justify-between pt-6">
                {getPreviousCategory() ? (
                  <Button 
                    type="button"
                    variant="outline" 
                    onClick={() => navigateToCategory(getPreviousCategory() as string)}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    {categories[getPreviousCategory() as keyof typeof categories]}
                  </Button>
                ) : (
                  <div></div>
                )}
                
                {getNextCategory() ? (
                  <Button 
                    type="button"
                    onClick={() => navigateToCategory(getNextCategory() as string)}
                    className="bg-brand-accent hover:bg-brand-accent/90"
                  >
                    {categories[getNextCategory() as keyof typeof categories]}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button 
                    type="submit"
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Finalizar Assessment
                    <CheckCircle className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </main>
      <Footer />
      
      {/* Add the AI Assistant */}
      <AIAssistant context="assessment" />
    </div>
  );
};

export default MaturityAssessment;
