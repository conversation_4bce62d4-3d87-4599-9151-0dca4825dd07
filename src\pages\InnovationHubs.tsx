
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, ArrowRight, Globe, Building, Lightbulb, Zap, Users, PieChart } from "lucide-react";
import HubFeatures from "@/components/innovation-hubs/HubFeatures";
import CaseStudies from "@/components/innovation-hubs/CaseStudies";

const InnovationHubs = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Innovation Hubs | ATAC PRO";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-28 pb-16 md:pt-36 md:pb-24 bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-montserrat text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Centros de Inovação<br />Globais
            </h1>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Nossa rede internacional de hubs de inovação ajuda empresas a se conectarem com os ecossistemas mais avançados do mundo.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link to="/contato">
                <Button size="lg" className="bg-white text-indigo-900 hover:bg-gray-100 font-medium px-8">
                  Falar com Especialista
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 font-medium px-8">
                Conhecer Metodologia
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Conectando empresas aos <span className="text-indigo-700">centros globais</span> de inovação
              </h2>
              <p className="text-lg text-gray-700 mb-6">
                Nossos Innovation Hubs são centros estrategicamente localizados nos principais ecossistemas de inovação do mundo, 
                criando uma ponte entre empresas brasileiras e os centros mais avançados de tecnologia e empreendedorismo.
              </p>
              <p className="text-lg text-gray-700 mb-8">
                Através dos hubs, oferecemos programas imersivos, conexões com startups locais, acesso a talentos 
                globais e insights sobre as últimas tendências tecnológicas de cada região.
              </p>
              
              <div className="space-y-4 mb-8">
                {[
                  "Acesso a ecossistemas de inovação globais",
                  "Conexões com startups e empreendedores locais",
                  "Imersões e programas personalizados",
                  "Monitoramento de tendências tecnológicas regionais"
                ].map((item, index) => (
                  <div key={index} className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-indigo-600 mr-3 flex-shrink-0" />
                    <p className="text-gray-700">{item}</p>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="rounded-xl overflow-hidden shadow-xl">
              <img 
                src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?auto=format&fit=crop&w=1170&q=80" 
                alt="Innovation Hub Team" 
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>
      
      {/* Features Section */}
      <HubFeatures />
      
      {/* Case Studies Section */}
      <CaseStudies />
      
      {/* Methodology Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Nossa <span className="text-indigo-700">Metodologia</span>
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Um processo estruturado para maximizar o retorno dos programas de inovação
            </p>
          </div>
          
          <div className="relative">
            <div className="absolute left-0 right-0 h-1 bg-gradient-to-r from-indigo-600 to-purple-600 top-1/2 transform -translate-y-1/2 hidden md:block"></div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                {
                  icon: PieChart,
                  title: "Diagnóstico",
                  description: "Identificação dos objetivos estratégicos e desafios específicos da empresa"
                },
                {
                  icon: Lightbulb,
                  title: "Co-criação",
                  description: "Desenvolvimento colaborativo de programas alinhados aos objetivos de negócio"
                },
                {
                  icon: Users,
                  title: "Imersão",
                  description: "Experiências no hub com mentoria especializada e conexões estratégicas"
                },
                {
                  icon: Zap,
                  title: "Implementação",
                  description: "Suporte contínuo para transformar aprendizados em resultados tangíveis"
                }
              ].map((step, index) => (
                <div key={index} className="relative">
                  <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 relative z-10">
                    <div className="w-16 h-16 rounded-full bg-indigo-100 flex items-center justify-center mx-auto mb-6 md:absolute md:top-0 md:left-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 md:border-4 md:border-white">
                      <step.icon className="h-8 w-8 text-indigo-600" />
                    </div>
                    <div className="md:mt-6">
                      <h3 className="font-montserrat text-xl font-semibold text-center mb-4">
                        {index + 1}. {step.title}
                      </h3>
                      <p className="text-gray-700 text-center">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
            Conecte sua empresa aos melhores ecossistemas de inovação
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Entre em contato conosco para conhecer mais sobre nossos Innovation Hubs e como podemos ajudar sua empresa.
          </p>
          <Link to="/contato">
            <Button size="lg" className="bg-white text-indigo-900 hover:bg-gray-100 font-medium px-8">
              Agende uma Consulta
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default InnovationHubs;
