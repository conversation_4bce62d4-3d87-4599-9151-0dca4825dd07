import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  BarChart3, 
  Target, 
  Users, 
  BookOpen, 
  Calendar, 
  Award, 
  TrendingUp, 
  MessageSquare,
  Download,
  PlayCircle,
  CheckCircle,
  Clock,
  ArrowRight,
  Star,
  Lightbulb,
  Rocket,
  Building
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

// Mock user data - in a real app, this would come from an API
const mockUserData = {
  name: "<PERSON>",
  company: "TechStart Inovação",
  memberSince: "Janeiro 2024",
  avatar: "/placeholder-avatar.jpg",
  assessments: {
    maturity: {
      completed: true,
      score: 7.2,
      level: "Crescimento",
      completedAt: "2024-01-15",
      nextRecommendedDate: "2024-04-15"
    },
    valuation: {
      completed: true,
      estimatedValue: "R$ 2.5M",
      confidence: 85,
      completedAt: "2024-01-20"
    }
  },
  journey: {
    currentPhase: "Validação de Mercado",
    progress: 65,
    nextMilestone: "Captação de Investimento",
    completedTasks: 12,
    totalTasks: 18
  },
  mentoring: {
    totalSessions: 3,
    nextSession: "2024-02-15 14:00",
    mentor: "Dra. Sofia Oliveira"
  },
  networking: {
    connections: 24,
    events: 2,
    opportunities: 5
  }
};

const Dashboard = () => {
  const [user] = useState(mockUserData);

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Dashboard | ATAC Academy";
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  Olá, {user.name}! 👋
                </h1>
                <p className="text-gray-600">
                  Bem-vindo ao seu painel de crescimento empresarial
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">Membro desde</p>
                <p className="font-semibold">{user.memberSince}</p>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg mr-4">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Score de Maturidade</p>
                    <p className="text-2xl font-bold text-blue-600">{user.assessments.maturity.score}/10</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg mr-4">
                    <Target className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Progresso da Jornada</p>
                    <p className="text-2xl font-bold text-green-600">{user.journey.progress}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg mr-4">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Conexões</p>
                    <p className="text-2xl font-bold text-purple-600">{user.networking.connections}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg mr-4">
                    <MessageSquare className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Sessões de Mentoria</p>
                    <p className="text-2xl font-bold text-orange-600">{user.mentoring.totalSessions}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-8">
              {/* Journey Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Rocket className="h-5 w-5 mr-2 text-brand" />
                    Sua Jornada de Crescimento
                  </CardTitle>
                  <CardDescription>
                    Acompanhe seu progresso na metodologia ATAC
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Fase Atual: {user.journey.currentPhase}</span>
                      <Badge variant="outline">{user.journey.progress}% completo</Badge>
                    </div>
                    <Progress value={user.journey.progress} className="h-3" />
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>{user.journey.completedTasks} de {user.journey.totalTasks} tarefas concluídas</span>
                      <span>Próximo: {user.journey.nextMilestone}</span>
                    </div>
                  </div>
                  <div className="mt-6">
                    <Link to="/personalized-journey">
                      <Button className="w-full">
                        Ver Jornada Completa
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Assessments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2 text-brand-accent" />
                    Seus Diagnósticos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Maturity Assessment */}
                    <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="p-2 bg-blue-100 rounded-lg mr-4">
                          <Target className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold">Assessment de Maturidade</h4>
                          <p className="text-sm text-gray-600">
                            Nível: {user.assessments.maturity.level} • Score: {user.assessments.maturity.score}/10
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className="bg-green-100 text-green-800">Concluído</Badge>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(user.assessments.maturity.completedAt).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>

                    {/* Valuation Assessment */}
                    <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="p-2 bg-green-100 rounded-lg mr-4">
                          <TrendingUp className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold">Diagnóstico de Valuation</h4>
                          <p className="text-sm text-gray-600">
                            Valor estimado: {user.assessments.valuation.estimatedValue}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className="bg-green-100 text-green-800">Concluído</Badge>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(user.assessments.valuation.completedAt).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 flex gap-3">
                    <Button variant="outline" className="flex-1">
                      <Download className="mr-2 h-4 w-4" />
                      Baixar Relatórios
                    </Button>
                    <Link to="/diagnostic" className="flex-1">
                      <Button variant="outline" className="w-full">
                        Refazer Assessment
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column */}
            <div className="space-y-8">
              {/* Next Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Próximas Ações
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
                      <Clock className="h-4 w-4 text-yellow-600 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Sessão de Mentoria</p>
                        <p className="text-xs text-gray-600">{user.mentoring.nextSession}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                      <BookOpen className="h-4 w-4 text-blue-600 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Novo Conteúdo Disponível</p>
                        <p className="text-xs text-gray-600">3 artigos recomendados</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                      <Users className="h-4 w-4 text-purple-600 mr-3" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Networking Event</p>
                        <p className="text-xs text-gray-600">Próxima quinta-feira</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Access */}
              <Card>
                <CardHeader>
                  <CardTitle>Acesso Rápido</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    <Link to="/content-library">
                      <Button variant="outline" className="w-full h-20 flex flex-col">
                        <BookOpen className="h-6 w-6 mb-2" />
                        <span className="text-xs">Biblioteca</span>
                      </Button>
                    </Link>
                    
                    <Link to="/networking-hub">
                      <Button variant="outline" className="w-full h-20 flex flex-col">
                        <Users className="h-6 w-6 mb-2" />
                        <span className="text-xs">Networking</span>
                      </Button>
                    </Link>
                    
                    <Link to="/management-tools">
                      <Button variant="outline" className="w-full h-20 flex flex-col">
                        <Target className="h-6 w-6 mb-2" />
                        <span className="text-xs">Ferramentas</span>
                      </Button>
                    </Link>
                    
                    <Link to="/mentoria">
                      <Button variant="outline" className="w-full h-20 flex flex-col">
                        <MessageSquare className="h-6 w-6 mb-2" />
                        <span className="text-xs">Mentoria</span>
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Dashboard;
