
import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import PurchaseAssistant from "@/components/PurchaseAssistant";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { 
  Clock, 
  Star, 
  BookOpen, 
  Rocket, 
  Globe, 
  Lightbulb, 
  Share2, 
  Target,
  BarChart2,
  Tag
} from "lucide-react";

const coursesByCategory = {
  startupse: [
    {
      id: "ai-na-pratica",
      title: "AI na Prática",
      description: "Aprenda a implementar soluções práticas de IA em seus projetos e negócios com foco em resultados tangíveis",
      image: "/lovable-uploads/ebdd26ce-4835-4e4c-91b6-abbfad51eac7.png",
      duration: "6 horas",
      students: 324,
      lessons: 6,
      workshops: 0,
      rating: 4.7,
      price: "R$ 2.500",
      priceAmount: 2500,
      promotionPrice: "R$ 1.750",
      hasPromotion: true,
      instructor: "Ricardo Alves",
      type: "Online",
      url: "/curso/ai-na-pratica"
    },
    {
      id: "ia-para-negocios",
      title: "IA para Negócios",
      description: "Programa presencial de 2 dias que ensina a aplicar Inteligência Artificial para otimizar operações empresariais",
      image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2940&auto=format&fit=crop",
      duration: "2 dias",
      students: 187,
      lessons: 4,
      workshops: 6,
      rating: 4.8,
      price: "R$ 2.497",
      priceAmount: 2497,
      instructor: "Mariana Costa",
      type: "Presencial",
      url: "/curso/ia-para-negocios"
    },
    {
      id: "data-driven-growth",
      title: "Data Driven Growth",
      description: "Aprenda a usar dados para impulsionar o crescimento do seu negócio com decisões baseadas em métricas",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop",
      duration: "4 semanas",
      students: 210,
      lessons: 8,
      workshops: 4,
      rating: 4.8,
      price: "R$ 2.797",
      priceAmount: 2797,
      instructor: "Paulo Santos",
      type: "Online e ao vivo",
      url: "/curso/data-driven-growth"
    },
    {
      id: "ia-para-produtos",
      title: "IA para Produtos",
      description: "Programa executivo online e ao vivo de 8 semanas para aplicar IA nos negócios",
      image: "https://images.unsplash.com/photo-1677442135136-760c813a743d?q=80&w=2940&auto=format&fit=crop",
      duration: "8 semanas",
      students: 245,
      lessons: 16,
      workshops: 8,
      rating: 4.9,
      price: "R$ 3.997",
      priceAmount: 3997,
      instructor: "Carlos Mendes",
      type: "Online e ao vivo",
      url: "/curso/ia-para-produtos"
    }
  ],
  imersoes: [
    {
      id: 5,
      title: "Vale do Silício",
      description: "Imersão executiva de 6 dias no Vale do Silício com visitas às principais empresas de tecnologia",
      image: "https://images.unsplash.com/photo-1521747116042-5a810fda9664?q=80&w=2940&auto=format&fit=crop",
      duration: "6 dias",
      students: 78,
      lessons: 0,
      workshops: 12,
      rating: 4.9,
      price: "R$ 18.900",
      priceAmount: 18900,
      instructor: "Ana Silva",
      type: "Presencial Internacional",
      url: "https://www.startse.com/learning-experience/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    },
    {
      id: 6,
      title: "AI Learning Experience",
      description: "Experiência única de 5 dias no Vale do Silício, com visitas a empresas líderes em IA",
      image: "https://images.unsplash.com/photo-1557838429-06783d16b871?q=80&w=2940&auto=format&fit=crop",
      duration: "5 dias",
      students: 56,
      lessons: 0,
      workshops: 10,
      rating: 4.8,
      price: "R$ 17.500",
      priceAmount: 17500,
      instructor: "Juliana Martins",
      type: "Presencial Internacional",
      url: "https://www.startse.com/learning-experience/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    },
    {
      id: 7,
      title: "Imersão China Fintech",
      description: "Conecte-se às empresas Fintech do ecossistema mais avançado do mundo em meios de pagamento",
      image: "https://images.unsplash.com/photo-1461695008884-244cb4543d74?q=80&w=2940&auto=format&fit=crop",
      duration: "7 dias",
      students: 42,
      lessons: 0,
      workshops: 14,
      rating: 4.9,
      price: "R$ 22.900",
      priceAmount: 22900,
      instructor: "Roberto Oliveira",
      type: "Presencial Internacional",
      url: "https://www.startse.com/learning-experience/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    },
    {
      id: 8,
      title: "Imersão Israel",
      description: "Conheça o ecossistema de inovação e cibersegurança da startup nation",
      image: "https://images.unsplash.com/photo-1542742135136-760c813a743d?q=80&w=2940&auto=format&fit=crop",
      duration: "6 dias",
      students: 38,
      lessons: 0,
      workshops: 12,
      rating: 4.8,
      price: "R$ 19.900",
      priceAmount: 19900,
      instructor: "Gabriel Rocha",
      type: "Presencial Internacional",
      url: "https://www.startse.com/learning-experience/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    }
  ],
  acelere: [
    {
      id: "startup-bootcamp",
      title: "Startup Bootcamp",
      description: "Estação de base da ideia ao modelo e à estratégia do negócio para startups em fase inicial",
      image: "/lovable-uploads/d4c2a54f-d96c-4b89-a537-d20f73a5cd40.png",
      duration: "4 semanas",
      students: 85,
      lessons: 8,
      workshops: 4,
      rating: 4.8,
      price: "R$ 1.997",
      priceAmount: 1997,
      instructor: "Equipe ATAC",
      type: "Online e ao vivo",
      url: "/acelere-seu-negocio"
    },
    {
      id: "startup-revolution",
      title: "Startup Revolution",
      description: "Programa para empreendedores com ideias inovadoras que precisam de direcionamento para decolar",
      image: "/lovable-uploads/765a19e3-9747-45ff-b27b-4ae988011be8.png",
      duration: "6 semanas",
      students: 120,
      lessons: 12,
      workshops: 6,
      rating: 4.7,
      price: "R$ 2.997",
      priceAmount: 2997,
      instructor: "Equipe ATAC",
      type: "Online e ao vivo",
      url: "/acelere-seu-negocio"
    },
    {
      id: 9,
      title: "Programa de Aceleração",
      description: "Programa completo de 3 meses para escalar startups com produto validado e tração inicial",
      image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      duration: "3 meses",
      students: 18,
      lessons: 24,
      workshops: 12,
      rating: 4.9,
      price: "R$ 15.000",
      priceAmount: 15000,
      instructor: "Equipe ATAC",
      type: "Híbrido",
      url: "https://www.startse.com/executive-program/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    },
    {
      id: 10,
      title: "Growth Hacking",
      description: "Técnicas e estratégias para acelerar o crescimento do seu negócio de forma sustentável",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1115&q=80",
      duration: "8 semanas",
      students: 45,
      lessons: 16,
      workshops: 8,
      rating: 4.8,
      price: "R$ 4.997",
      priceAmount: 4997,
      instructor: "Marcos Vinicius",
      type: "Online e ao vivo",
      url: "https://www.startse.com/executive-program/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    },
    {
      id: 11,
      title: "Mentoria de Negócios",
      description: "Mentoria personalizada para empresas em crescimento com especialistas de mercado",
      image: "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      duration: "6 meses",
      students: 12,
      lessons: 24,
      workshops: 6,
      rating: 4.9,
      price: "R$ 24.000",
      priceAmount: 24000,
      instructor: "Carlos Mendes",
      type: "Híbrido",
      url: "https://www.startse.com/executive-program/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    },
    {
      id: 12,
      title: "Pitch Perfeito",
      description: "Treinamento intensivo para preparar seu pitch para captação de investimentos",
      image: "https://images.unsplash.com/photo-1579532537598-459ecdaf39cc?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      duration: "3 semanas",
      students: 32,
      lessons: 6,
      workshops: 3,
      rating: 4.8,
      price: "R$ 3.497",
      priceAmount: 3497,
      instructor: "Tatiana Mello",
      type: "Online e ao vivo",
      url: "https://www.startse.com/executive-program/?utm_source=pagina-de-categorias&utm_medium=card-categoria&utm_campaign=card-categoria-produtos"
    }
  ],
  openinnovation: [
    {
      id: 13,
      title: "Liderança Feminina",
      description: "Formação completa para desenvolvimento de habilidades e superação de desafios de carreira",
      image: "https://images.unsplash.com/photo-1573497701240-345a300b8d36?q=80&w=2940&auto=format&fit=crop",
      duration: "2 meses",
      students: 64,
      lessons: 16,
      workshops: 8,
      rating: 4.9,
      price: "R$ 5.997",
      priceAmount: 5997,
      instructor: "Renata Souza",
      type: "Híbrido",
      url: "/open-innovation",
      showPrice: false
    },
    {
      id: 14,
      title: "Executive Program",
      description: "Imersão presencial de 3 dias para executivos focada em liderança, inovação e transformação digital",
      image: "https://images.unsplash.com/photo-1542744173-05336fcc7ad4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      duration: "3 dias",
      students: 74,
      lessons: 6,
      workshops: 4,
      rating: 4.8,
      price: "R$ 5.800",
      priceAmount: 5800,
      instructor: "Eduardo Santos",
      type: "Presencial",
      url: "/open-innovation",
      showPrice: false
    },
    {
      id: 15,
      title: "Board Program",
      description: "Imersão híbrida de 5 meses que combina aulas gravadas e encontros presenciais para formar conselheiros",
      image: "https://images.unsplash.com/photo-1559526324-593bc073d938?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      duration: "5 meses",
      students: 36,
      lessons: 20,
      workshops: 3,
      rating: 4.9,
      price: "R$ 12.900",
      priceAmount: 12900,
      instructor: "Carolina Mendes",
      type: "Híbrido",
      url: "/open-innovation",
      showPrice: false
    },
    {
      id: 16,
      title: "Certificação em Open Innovation",
      description: "Programa completo para implementar inovação aberta e colaborativa em sua organização",
      image: "https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      duration: "10 semanas",
      students: 48,
      lessons: 20,
      workshops: 5,
      rating: 4.8,
      price: "R$ 7.900",
      priceAmount: 7900,
      instructor: "Fernanda Lima",
      type: "Online e ao vivo",
      url: "/open-innovation",
      showPrice: false
    }
  ]
};

const sortCoursesByPrice = (courses) => {
  return [...courses].sort((a, b) => a.priceAmount - b.priceAmount);
};

Object.keys(coursesByCategory).forEach(category => {
  coursesByCategory[category] = sortCoursesByPrice(coursesByCategory[category]);
});

const CourseCard = ({ course }) => {
  const getUrl = (course) => {
    if (course.type.includes("Internacional")) {
      return "/learning-experience";
    }
    if (coursesByCategory.acelere.some(c => c.id === course.id)) {
      return "/acelere-seu-negocio";
    }
    if (coursesByCategory.openinnovation.some(c => c.id === course.id)) {
      return "/open-innovation";
    }
    return course.url;
  };

  const isOpenInnovation = coursesByCategory.openinnovation.some(c => c.id === course.id);

  return (
    <Card className="overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300 group hover:-translate-y-1">
      <div className="relative h-52 overflow-hidden">
        <img 
          src={course.image} 
          alt={course.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute top-4 right-4 bg-brand-accent text-white text-sm font-medium py-1 px-3 rounded-full">
          {course.type}
        </div>
        {course.hasPromotion && (
          <div className="absolute top-4 left-4 bg-red-500 text-white text-sm font-medium py-1 px-3 rounded-full flex items-center">
            <Tag className="h-3 w-3 mr-1" />
            Promoção
          </div>
        )}
      </div>
      <CardHeader className="p-6 pb-2">
        <h3 className="font-montserrat text-xl font-semibold text-gray-900 group-hover:text-brand transition-colors">
          {course.title}
        </h3>
      </CardHeader>
      <CardContent className="p-6 pt-2 pb-4">
        <p className="text-gray-700 mb-4 line-clamp-2">{course.description}</p>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-brand-accent mr-2" />
            <span className="text-sm">{course.duration}</span>
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-2" />
            <span className="text-sm">{course.rating}</span>
          </div>
          {course.lessons > 0 && (
            <div className="flex items-center">
              <BookOpen className="h-4 w-4 text-brand mr-2" />
              <span className="text-sm">{course.lessons} aulas</span>
            </div>
          )}
          <div className="flex items-center">
            <span className="text-sm">{course.workshops} workshops</span>
          </div>
        </div>
        {(!isOpenInnovation && course.showPrice !== false) && (
          <div className="flex flex-col items-end mt-4">
            {course.hasPromotion ? (
              <>
                <span className="text-gray-500 line-through text-sm">{course.price}</span>
                <span className="text-red-500 font-bold text-xl">{course.promotionPrice}</span>
              </>
            ) : (
              <span className="text-brand font-bold text-xl">{course.price}</span>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="p-6 pt-0">
        <Link to={getUrl(course)} className="w-full">
          <Button className="w-full bg-brand hover:bg-brand-secondary">
            Saiba Mais
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
};

const CategoryHeader = ({ icon, title, description }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-3">
        {icon}
        <h2 className="font-montserrat text-2xl md:text-3xl font-bold text-gray-900 ml-3">
          {title}
        </h2>
      </div>
      <p className="text-lg text-gray-700">
        {description}
      </p>
    </div>
  );
};

const ProgramsSlider = ({ category, courses }) => {
  return (
    <div className="relative">
      <div className="flex overflow-x-auto pb-6 gap-6 snap-x scrollbar-hide">
        {courses.map(course => (
          <div key={course.id} className="min-w-[320px] md:min-w-[360px] snap-start">
            <CourseCard course={course} />
          </div>
        ))}
      </div>
    </div>
  );
};

const Cursos = () => {
  const [activeTab, setActiveTab] = useState("all");
  
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Programas de Aceleração | ATAC Academy";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <Hero 
        smallText="PROGRAMAS DE ACELERAÇÃO"
        title="Programas de <span>Aceleração</span> e <span class='text-brand-accent'>Capacitação</span>"
        description="Impulsione sua jornada empreendedora com nossos programas de longa duração e conteúdos especializados para diferentes estágios do seu negócio."
        primaryButtonText="Ver Todos os Programas"
        primaryButtonLink="#programas"
        secondaryButtonText="Fale com um Consultor"
        secondaryButtonLink="/contato"
        imageUrl="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        showCarousel={false}
        stats={[
          { text: "+500 Alunos Formados", icon: Target },
          { text: "97% de Satisfação", icon: Star }
        ]}
      />
      <main className="py-20" id="programas">
        <div className="container mx-auto px-4">          
          <div className="mb-12">
            <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
              <div className="flex justify-center mb-8 overflow-x-auto snap-x pb-4">
                <TabsList className="bg-gray-100 p-1 rounded-full">
                  <TabsTrigger 
                    value="all" 
                    className="rounded-full px-6 py-2 data-[state=active]:bg-brand data-[state=active]:text-white"
                  >
                    Todos
                  </TabsTrigger>
                  <TabsTrigger 
                    value="startupse" 
                    className="rounded-full px-6 py-2 data-[state=active]:bg-brand data-[state=active]:text-white whitespace-nowrap"
                  >
                    Startup-se
                  </TabsTrigger>
                  <TabsTrigger 
                    value="imersoes" 
                    className="rounded-full px-6 py-2 data-[state=active]:bg-brand data-[state=active]:text-white whitespace-nowrap"
                  >
                    Imersões
                  </TabsTrigger>
                  <TabsTrigger 
                    value="acelere" 
                    className="rounded-full px-6 py-2 data-[state=active]:bg-brand data-[state=active]:text-white whitespace-nowrap"
                  >
                    Acelere seu Negócio
                  </TabsTrigger>
                  <TabsTrigger 
                    value="openinnovation" 
                    className="rounded-full px-6 py-2 data-[state=active]:bg-brand data-[state=active]:text-white whitespace-nowrap"
                  >
                    Open Innovation
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="all" className="mt-6">
                <div className="mb-16">
                  <CategoryHeader 
                    icon={<div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center"><Lightbulb className="h-5 w-5 text-brand" /></div>}
                    title="Startup-se" 
                    description="Programas focados em capacitação para empreendedores que desejam iniciar ou alavancar seus negócios"
                  />
                  <ProgramsSlider category="startupse" courses={coursesByCategory.startupse} />
                </div>
                
                <div className="mb-16">
                  <CategoryHeader 
                    icon={<div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center"><Globe className="h-5 w-5 text-brand-accent" /></div>}
                    title="Imersões" 
                    description="Experiências internacionais para conhecer ecosistemas de inovação pelo mundo"
                  />
                  <ProgramsSlider category="imersoes" courses={coursesByCategory.imersoes} />
                </div>
                
                <div className="mb-16">
                  <CategoryHeader 
                    icon={<div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center"><Rocket className="h-5 w-5 text-green-600" /></div>}
                    title="Acelere seu Negócio" 
                    description="Programas de aceleração para empresas com produto validado buscando escala"
                  />
                  <ProgramsSlider category="acelere" courses={coursesByCategory.acelere} />
                </div>
                
                <div>
                  <CategoryHeader 
                    icon={<div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center"><Share2 className="h-5 w-5 text-amber-600" /></div>}
                    title="Open Innovation" 
                    description="Programas de inovação aberta para executivos e líderes"
                  />
                  <ProgramsSlider category="openinnovation" courses={coursesByCategory.openinnovation} />
                </div>
              </TabsContent>
              
              <TabsContent value="startupse">
                <CategoryHeader 
                  icon={<div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center"><Lightbulb className="h-5 w-5 text-brand" /></div>}
                  title="Startup-se" 
                  description="Programas focados em capacitação para empreendedores que desejam iniciar ou alavancar seus negócios"
                />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {coursesByCategory.startupse.map(course => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="imersoes">
                <CategoryHeader 
                  icon={<div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center"><Globe className="h-5 w-5 text-brand-accent" /></div>}
                  title="Imersões" 
                  description="Experiências internacionais para conhecer ecosistemas de inovação pelo mundo"
                />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {coursesByCategory.imersoes.map(course => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="acelere">
                <CategoryHeader 
                  icon={<div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center"><Rocket className="h-5 w-5 text-green-600" /></div>}
                  title="Acelere seu Negócio" 
                  description="Programas de aceleração para empresas com produto validado buscando escala"
                />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {coursesByCategory.acelere.map(course => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="openinnovation">
                <CategoryHeader 
                  icon={<div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center"><Share2 className="h-5 w-5 text-amber-600" /></div>}
                  title="Open Innovation" 
                  description="Programas de inovação aberta para executivos e líderes"
                />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {coursesByCategory.openinnovation.map(course => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
      
      {/* Add Purchase Assistant */}
      <PurchaseAssistant productName="Cursos e Programas" productType="Online e Presencial" />
      
      <Footer />
    </div>
  );
};

export default Cursos;
