import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  Users, 
  MessageSquare, 
  Calendar, 
  MapPin, 
  Building, 
  Star,
  Filter,
  Search,
  UserPlus,
  Video,
  Coffee,
  Briefcase,
  TrendingUp,
  Target,
  Globe,
  Award,
  Clock
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Mock networking data
const networkMembers = [
  {
    id: 1,
    name: "<PERSON>",
    title: "CEO",
    company: "TechStart",
    sector: "Tecnologia",
    location: "São Paulo, SP",
    avatar: "/placeholder-avatar.jpg",
    connections: 45,
    rating: 4.9,
    expertise: ["Captação", "Gestão", "Tecnologia"],
    lookingFor: "Investidores, Parceiros Técnicos",
    offering: "Mentoria em Gestão, Networking",
    bio: "Empreendedora serial com 10+ anos de experiência em startups de tecnologia.",
    verified: true
  },
  {
    id: 2,
    name: "Carlos Mendes",
    title: "Investidor Anjo",
    company: "Angel Ventures",
    sector: "Investimentos",
    location: "Rio de Janeiro, RJ",
    avatar: "/placeholder-avatar.jpg",
    connections: 120,
    rating: 4.8,
    expertise: ["Investimento", "Valuation", "Estratégia"],
    lookingFor: "Startups em Série A",
    offering: "Capital, Mentoria Estratégica",
    bio: "Investidor anjo com portfolio de 30+ startups e 15 anos no mercado financeiro.",
    verified: true
  },
  {
    id: 3,
    name: "Mariana Costa",
    title: "Head of Marketing",
    company: "Growth Labs",
    sector: "Marketing",
    location: "Belo Horizonte, MG",
    avatar: "/placeholder-avatar.jpg",
    connections: 78,
    rating: 4.7,
    expertise: ["Growth Hacking", "Marketing Digital", "Branding"],
    lookingFor: "Startups para Consultoria",
    offering: "Estratégias de Growth, Mentoria",
    bio: "Especialista em growth com cases de sucesso em 50+ startups.",
    verified: false
  }
];

const upcomingEvents = [
  {
    id: 1,
    title: "Meetup: Captação de Investimento",
    type: "presencial",
    date: "2024-02-15",
    time: "19:00",
    location: "São Paulo, SP",
    attendees: 45,
    maxAttendees: 60,
    description: "Encontro para discutir estratégias de captação e networking com investidores.",
    organizer: "ATAC Academy",
    tags: ["captação", "investimento", "networking"]
  },
  {
    id: 2,
    title: "Workshop Online: Pitch Perfect",
    type: "online",
    date: "2024-02-20",
    time: "14:00",
    location: "Online",
    attendees: 120,
    maxAttendees: 200,
    description: "Workshop prático para aperfeiçoar seu pitch e conquistar investidores.",
    organizer: "Carlos Mendes",
    tags: ["pitch", "apresentação", "workshop"]
  },
  {
    id: 3,
    title: "Coffee & Connections",
    type: "presencial",
    date: "2024-02-25",
    time: "08:30",
    location: "Rio de Janeiro, RJ",
    attendees: 25,
    maxAttendees: 30,
    description: "Café da manhã informal para networking entre empreendedores.",
    organizer: "Rede ATAC RJ",
    tags: ["networking", "informal", "café"]
  }
];

const opportunities = [
  {
    id: 1,
    title: "Busco CTO para Fintech",
    type: "partnership",
    company: "PayFlow",
    sector: "Fintech",
    description: "Startup de pagamentos busca CTO com experiência em blockchain e sistemas financeiros.",
    requirements: ["5+ anos em desenvolvimento", "Experiência com blockchain", "Liderança técnica"],
    contact: "<EMAIL>",
    postedAt: "2024-02-01",
    urgent: true
  },
  {
    id: 2,
    title: "Investimento Série A - HealthTech",
    type: "investment",
    company: "MedConnect",
    sector: "HealthTech",
    description: "Plataforma de telemedicina busca investimento de R$ 2M para expansão nacional.",
    requirements: ["Tração comprovada", "Modelo escalável", "Equipe experiente"],
    contact: "<EMAIL>",
    postedAt: "2024-01-28",
    urgent: false
  },
  {
    id: 3,
    title: "Parceria Comercial - EdTech",
    type: "partnership",
    company: "EduTech Solutions",
    sector: "EdTech",
    description: "Busco parceiros para distribuição de plataforma educacional em universidades.",
    requirements: ["Rede de contatos em educação", "Experiência em vendas B2B"],
    contact: "<EMAIL>",
    postedAt: "2024-01-25",
    urgent: false
  }
];

const NetworkingHub = () => {
  const [activeTab, setActiveTab] = useState("members");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSector, setSelectedSector] = useState("Todos");
  const [selectedLocation, setSelectedLocation] = useState("Todos");

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Hub de Networking | ATAC Academy";
  }, []);

  const sectors = ["Todos", "Tecnologia", "Fintech", "HealthTech", "EdTech", "Marketing", "Investimentos"];
  const locations = ["Todos", "São Paulo, SP", "Rio de Janeiro, RJ", "Belo Horizonte, MG"];

  const getEventTypeIcon = (type: string) => {
    return type === "online" ? <Video className="h-4 w-4" /> : <MapPin className="h-4 w-4" />;
  };

  const getOpportunityTypeIcon = (type: string) => {
    return type === "investment" ? <TrendingUp className="h-4 w-4" /> : <UserPlus className="h-4 w-4" />;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Hub de <span className="text-brand">Networking</span>
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Conecte-se com empreendedores, investidores e especialistas do ecossistema ATAC
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-brand mb-1">{networkMembers.length}+</div>
                <div className="text-sm text-gray-600">Membros ativos</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-brand-accent mb-1">{upcomingEvents.length}</div>
                <div className="text-sm text-gray-600">Eventos este mês</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-green-600 mb-1">{opportunities.length}</div>
                <div className="text-sm text-gray-600">Oportunidades</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-purple-600 mb-1">95%</div>
                <div className="text-sm text-gray-600">Taxa de match</div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto mb-8">
              <TabsTrigger value="members">Membros</TabsTrigger>
              <TabsTrigger value="events">Eventos</TabsTrigger>
              <TabsTrigger value="opportunities">Oportunidades</TabsTrigger>
            </TabsList>

            {/* Members Tab */}
            <TabsContent value="members">
              {/* Filters */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Buscar membros
                    </label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Nome, empresa ou expertise..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Setor
                    </label>
                    <Select value={selectedSector} onValueChange={setSelectedSector}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {sectors.map((sector) => (
                          <SelectItem key={sector} value={sector}>
                            {sector}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Localização
                    </label>
                    <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location} value={location}>
                            {location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Members Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {networkMembers.map((member) => (
                  <Card key={member.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-brand/10 rounded-full flex items-center justify-center mr-3">
                            <Users className="h-6 w-6 text-brand" />
                          </div>
                          <div>
                            <CardTitle className="text-lg flex items-center">
                              {member.name}
                              {member.verified && (
                                <Award className="h-4 w-4 text-blue-600 ml-2" />
                              )}
                            </CardTitle>
                            <CardDescription>
                              {member.title} • {member.company}
                            </CardDescription>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center text-sm text-gray-600">
                          <Building className="h-4 w-4 mr-2" />
                          {member.sector}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-2" />
                          {member.location}
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1 text-gray-500" />
                            {member.connections} conexões
                          </div>
                          <div className="flex items-center">
                            <Star className="h-4 w-4 mr-1 text-yellow-500" />
                            {member.rating}
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div>
                            <p className="text-xs font-medium text-gray-700">Expertise:</p>
                            <div className="flex flex-wrap gap-1">
                              {member.expertise.slice(0, 3).map((skill) => (
                                <Badge key={skill} variant="secondary" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <p className="text-xs font-medium text-gray-700">Busca:</p>
                            <p className="text-xs text-gray-600">{member.lookingFor}</p>
                          </div>
                          
                          <div>
                            <p className="text-xs font-medium text-gray-700">Oferece:</p>
                            <p className="text-xs text-gray-600">{member.offering}</p>
                          </div>
                        </div>
                        
                        <div className="flex gap-2 pt-3">
                          <Button size="sm" className="flex-1">
                            <MessageSquare className="h-4 w-4 mr-1" />
                            Conectar
                          </Button>
                          <Button size="sm" variant="outline">
                            Ver Perfil
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Events Tab */}
            <TabsContent value="events">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {upcomingEvents.map((event) => (
                  <Card key={event.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          {getEventTypeIcon(event.type)}
                          <Badge variant="outline" className="ml-2">
                            {event.type === "online" ? "Online" : "Presencial"}
                          </Badge>
                        </div>
                        <Badge className="bg-blue-100 text-blue-800">
                          {event.attendees}/{event.maxAttendees}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg">{event.title}</CardTitle>
                      <CardDescription>{event.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="h-4 w-4 mr-2" />
                          {new Date(event.date).toLocaleDateString('pt-BR')} às {event.time}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-2" />
                          {event.location}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Users className="h-4 w-4 mr-2" />
                          Organizado por {event.organizer}
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {event.tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        
                        <Button className="w-full mt-4">
                          Participar do Evento
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Opportunities Tab */}
            <TabsContent value="opportunities">
              <div className="space-y-6">
                {opportunities.map((opportunity) => (
                  <Card key={opportunity.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {getOpportunityTypeIcon(opportunity.type)}
                          <div className="ml-3">
                            <CardTitle className="text-lg flex items-center">
                              {opportunity.title}
                              {opportunity.urgent && (
                                <Badge className="ml-2 bg-red-100 text-red-800">
                                  Urgente
                                </Badge>
                              )}
                            </CardTitle>
                            <CardDescription>
                              {opportunity.company} • {opportunity.sector}
                            </CardDescription>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {opportunity.type === "investment" ? "Investimento" : "Parceria"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 mb-4">{opportunity.description}</p>
                      
                      <div className="mb-4">
                        <h4 className="font-medium text-sm mb-2">Requisitos:</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {opportunity.requirements.map((req, index) => (
                            <li key={index} className="flex items-center">
                              <div className="w-1.5 h-1.5 bg-brand rounded-full mr-2" />
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-gray-500">
                          Publicado em {new Date(opportunity.postedAt).toLocaleDateString('pt-BR')}
                        </div>
                        <Button>
                          Entrar em Contato
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default NetworkingHub;
