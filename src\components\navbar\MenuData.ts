
import { FileText, SearchCheck, Brain, Newspaper, BookOpen, ArrowRight } from "lucide-react";
import React from "react";

export interface MenuLink {
  label: string;
  url: string;
  icon?: React.ReactNode;
}

export interface MenuSection {
  title: string;
  key: string;
  links: MenuLink[];
}

export const menuData: MenuSection[] = [
  {
    title: "O QUE FAZEMOS",
    key: "o-que-fazemos",
    links: [
      { label: "Cursos", url: "/cursos" },
      { label: "Mentoria", url: "/mentoria" },
      { 
        label: "Diagnóstico Empresarial", 
        url: "/diagnostic",
        icon: React.createElement(SearchCheck, { className: "mr-2 text-brand-accent", size: 16 })
      },
      { 
        label: "Due Dilligence", 
        url: "/due-diligence",
        icon: React.createElement(FileText, { className: "mr-2 text-brand-accent", size: 16 })
      }
    ]
  },
  {
    title: "OPEN INNOVATION",
    key: "open-innovation",
    links: [
      { label: "Sobre o programa", url: "/open-innovation" },
      { label: "Corporate Challenges", url: "/corporate-challenges" },
      { label: "Innovation Hubs", url: "/innovation-hubs" },
      { label: "Cases de Sucesso", url: "/cases-sucesso" }
    ]
  },
  {
    title: "INVESTIDORES",
    key: "investidores",
    links: [
      { label: "Portal do Investidor", url: "/investidores" },
      { label: "Oportunidades", url: "/investidores/oportunidades" }
    ]
  },
  {
    title: "CONTEÚDOS",
    key: "conteudos",
    links: [
      { 
        label: "Blog ATAC Pro", 
        url: "/blog",
        icon: React.createElement(ArrowRight, { className: "mr-2 text-brand-accent", size: 16 })
      },
      { 
        label: "ATAC Prodcast", 
        url: "/blog/podcast" 
      },
      { 
        label: "Na Mídia", 
        url: "/na-midia",
        icon: React.createElement(ArrowRight, { className: "mr-2 text-brand-accent", size: 16 })
      }
    ]
  },
  {
    title: "QUEM SOMOS",
    key: "quem-somos",
    links: [
      { label: "Nossa Essência", url: "/quem-somos" },
      { 
        label: "Supermente ATAC Pro", 
        url: "/framework",
        icon: React.createElement(Brain, { className: "mr-2 text-brand-accent", size: 16 })
      },
      { label: "Parceiros", url: "/parcerias" },
      { label: "Contato", url: "/contato" }
    ]
  }
];
