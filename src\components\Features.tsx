
import { Book<PERSON><PERSON>, Users, TrendingUp, <PERSON>, <PERSON><PERSON><PERSON>, Zap } from "lucide-react";

const featuresData = [
  {
    title: "Cursos Práticos",
    description: "Conteúdo focado em resultados, desenvolvido por empreendedores de sucesso",
    icon: BookOpen
  },
  {
    title: "Mentoria Especializada",
    description: "Acompanhamento personalizado para acelerar o crescimento do seu negócio",
    icon: Users
  },
  {
    title: "Acesso a Investidores",
    description: "Conexão direta com nossa rede de investidores anjos e fundos de venture capital",
    icon: TrendingUp
  },
  {
    title: "Networking Estratégico",
    description: "Faça parte de uma comunidade seleta de empreendedores e especialistas",
    icon: Award
  },
  {
    title: "Métricas e Análises",
    description: "Aprenda a tomar decisões baseadas em dados e acompanhar resultados",
    icon: PieChart
  },
  {
    title: "Aceleração de Resultados",
    description: "Metodologias comprovadas para escalar seu negócio de forma sustentável",
    icon: Zap
  }
];

const Features = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Por que escolher a <span className="text-brand">ATAC PRO</span>?
          </h2>
          <p className="text-xl text-gray-700 max-w-2xl mx-auto">
            Nossa abordagem integrada reúne conhecimento, experiência e oportunidades para impulsionar seu sucesso empresarial.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuresData.map((feature, index) => (
            <div key={index} className="bg-white rounded-lg p-10 shadow-lg border-t-4 border-t-brand hover:border-t-brand-accent transition-colors duration-300">
              <div className="w-14 h-14 rounded-full bg-brand/10 flex items-center justify-center mb-6">
                <feature.icon className="w-7 h-7 text-brand" />
              </div>
              <h3 className="font-montserrat text-2xl font-semibold text-gray-900 mb-4">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-lg">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
