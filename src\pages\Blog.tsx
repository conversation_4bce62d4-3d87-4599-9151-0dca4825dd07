
import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, ExternalLink, Clock, Tag } from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

const Blog = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Blog | ATAC Pro";
  }, []);

  // Mock data for articles
  const featuredArticles = [
    {
      id: 1,
      title: "Como captar investimento para sua startup em 2024",
      excerpt: "Descubra as estratégias mais eficazes para atrair investidores e levantar capital para sua startup no cenário atual.",
      image: "https://images.unsplash.com/photo-1559526324-4b87b5e36e44?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      category: "Investimentos",
      date: "12 Jun 2024",
      readTime: "8 min"
    },
    {
      id: 2,
      title: "Os 5 erros mais comuns ao apresentar seu pitch para investidores",
      excerpt: "Saiba quais são os erros que podem prejudicar sua chance de receber investimento e aprenda como evitá-los.",
      image: "https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      category: "Pitch",
      date: "5 Jun 2024",
      readTime: "6 min"
    },
  ];

  const recentArticles = [
    {
      id: 3,
      title: "Valuation: Como determinar o valor da sua startup",
      excerpt: "Um guia prático sobre os métodos de valuation mais utilizados no mercado e como aplicá-los ao seu negócio.",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      category: "Finanças",
      date: "1 Jun 2024",
      readTime: "10 min"
    },
    {
      id: 4,
      title: "Gestão de equipes em startups: Desafios e soluções",
      excerpt: "Conheça as melhores práticas para gerenciar times em empresas de crescimento acelerado.",
      image: "https://images.unsplash.com/photo-1517048676732-d65bc937f952?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      category: "Gestão",
      date: "28 Mai 2024",
      readTime: "7 min"
    },
    {
      id: 5,
      title: "Insights do mercado de Venture Capital no Brasil",
      excerpt: "Análise das tendências e movimentações do mercado de VC no ecossistema brasileiro de inovação.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      category: "Mercado",
      date: "20 Mai 2024",
      readTime: "9 min"
    },
    {
      id: 6,
      title: "Como estruturar um MVP de sucesso",
      excerpt: "Passo a passo para criar um MVP que valide sua ideia e atraia os primeiros clientes.",
      image: "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      category: "Produto",
      date: "15 Mai 2024",
      readTime: "8 min"
    },
  ];

  const categories = [
    "Investimentos", "Pitch", "Finanças", "Gestão", 
    "Mercado", "Produto", "Marketing", "Tecnologia"
  ];

  const sponsors = [
    {
      id: 1,
      name: "StartupBR",
      logo: "https://via.placeholder.com/150x60?text=StartupBR",
      url: "#"
    },
    {
      id: 2,
      name: "Inovação Capital",
      logo: "https://via.placeholder.com/150x60?text=InovaçãoCapital",
      url: "#"
    },
    {
      id: 3,
      name: "Tech Ventures",
      logo: "https://via.placeholder.com/150x60?text=TechVentures",
      url: "#"
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        {/* Featured Articles Header */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-4 py-8">
            <div className="flex justify-between items-center mb-6">
              <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900">
                Blog <span className="text-brand">ATAC</span> <span className="text-brand-accent">Pro</span>
              </h1>
              <div className="hidden md:flex space-x-3">
                {categories.slice(0, 4).map((category) => (
                  <Button key={category} variant="outline" size="sm" className="text-gray-700">
                    {category}
                  </Button>
                ))}
                <Button variant="outline" size="sm" className="text-gray-700">
                  Mais <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
            <p className="text-xl text-gray-700 max-w-3xl">
              Conteúdo exclusivo sobre empreendedorismo, inovação, captação de investimentos e gestão de negócios.
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 mt-8">
          {/* Featured Articles Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {featuredArticles.map((article) => (
              <div key={article.id} className="group relative overflow-hidden rounded-xl shadow-md hover:shadow-xl transition-all duration-300">
                <div className="relative h-64 md:h-80 overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-30 z-10 transition-opacity group-hover:bg-opacity-20"></div>
                  <img 
                    src={article.image} 
                    alt={article.title} 
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute top-4 left-4 z-20">
                    <span className="bg-brand text-white px-3 py-1 text-sm font-medium rounded-full">
                      {article.category}
                    </span>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 p-6 z-20 bg-gradient-to-t from-black/80 to-transparent">
                    <h2 className="text-2xl font-bold text-white mb-2 group-hover:text-brand-accent transition-colors">
                      {article.title}
                    </h2>
                    <div className="flex items-center text-white/90 text-sm">
                      <span>{article.date}</span>
                      <span className="mx-2">•</span>
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{article.readTime} leitura</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Sponsors Section */}
          <div className="bg-white rounded-xl p-6 mb-12 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-gray-900">Patrocinadores</h3>
              <Button variant="link" className="text-brand">
                Seja um patrocinador <ExternalLink className="h-4 w-4 ml-1" />
              </Button>
            </div>
            <Separator className="my-4" />
            <div className="flex flex-wrap justify-center gap-8 py-2">
              {sponsors.map((sponsor) => (
                <a 
                  key={sponsor.id} 
                  href={sponsor.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="grayscale hover:grayscale-0 transition-all duration-300"
                >
                  <img 
                    src={sponsor.logo} 
                    alt={sponsor.name} 
                    className="h-12 md:h-16 object-contain"
                  />
                </a>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Articles List */}
            <div className="lg:col-span-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Artigos Recentes</h2>
              <div className="space-y-8">
                {recentArticles.map((article) => (
                  <Card key={article.id} className="overflow-hidden hover:shadow-md transition-shadow">
                    <div className="flex flex-col md:flex-row">
                      <div className="md:w-1/3 h-48 md:h-auto">
                        <img 
                          src={article.image} 
                          alt={article.title} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <CardContent className="md:w-2/3 p-6">
                        <div className="flex items-center mb-2">
                          <span className="text-sm font-medium text-brand mr-2">
                            {article.category}
                          </span>
                          <span className="text-sm text-gray-500">
                            {article.date} • {article.readTime} leitura
                          </span>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2 hover:text-brand transition-colors">
                          {article.title}
                        </h3>
                        <p className="text-gray-600 mb-4">{article.excerpt}</p>
                        <Link to="#" className="text-brand hover:text-brand-secondary font-medium flex items-center">
                          Leia mais <ArrowRight className="ml-1 h-4 w-4" />
                        </Link>
                      </CardContent>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="mt-10 text-center">
                <Button className="bg-brand hover:bg-brand-secondary">
                  Carregar mais artigos
                </Button>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-4">
              {/* Categories */}
              <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Categorias</h3>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <Link 
                      key={category} 
                      to="#" 
                      className="px-3 py-2 bg-gray-100 hover:bg-brand hover:text-white rounded-full text-sm font-medium text-gray-700 transition-colors"
                    >
                      {category}
                    </Link>
                  ))}
                </div>
              </div>

              {/* Newsletter Subscription */}
              <div className="bg-brand rounded-xl shadow-sm p-6 text-white">
                <h3 className="text-xl font-bold mb-3">Newsletter</h3>
                <p className="mb-4">Receba as últimas atualizações e conteúdos exclusivos diretamente no seu email.</p>
                <form className="space-y-3">
                  <input 
                    type="email" 
                    placeholder="Seu melhor email" 
                    className="w-full px-4 py-2 rounded-md border border-brand-light bg-white/90 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"
                  />
                  <Button className="w-full bg-white text-brand hover:bg-gray-100">
                    Inscrever-se
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Blog;
