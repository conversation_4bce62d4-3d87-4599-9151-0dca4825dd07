
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, DollarSign, BarChart, Users, Target } from "lucide-react";

const InvestorNetwork = ({ 
  smallText = "REDE DE INVESTIDORES", 
  title = "Acesse Nossa Rede de <span>Investidores</span> e <span class='text-brand-accent'>Parceiros</span>",
  description = "Conectamos empreendedores promissores com investidores anjos, fundos de venture capital e empresas parceiras que podem impulsionar o crescimento do seu negócio.",
  primaryButtonText = "Conheça Nossa Rede de Investidores",
  primaryButtonLink = "/investidores",
  secondaryButtonText = "",
  secondaryButtonLink = "",
  statValue = "R$ 15M+",
  statText = "Investidos no último ano",
  features = [
    {
      icon: DollarSign,
      title: "Capital Inteligente",
      description: "Mais que dinheiro, oferecemos suporte estratégico"
    },
    {
      icon: <PERSON><PERSON><PERSON>,
      title: "<PERSON>é<PERSON>as <PERSON>",
      description: "Preparamos seu negócio para ser investível"
    },
    {
      icon: Users,
      title: "Networking Estratégico",
      description: "Acesso a uma rede exclusiva de contatos"
    },
    {
      icon: Target,
      title: "Mentoria Especializada",
      description: "Orientação para cada etapa do seu crescimento"
    }
  ],
  className = ""
}) => {
  // Parse HTML in title with improved regex handling
  const renderTitle = (text) => {
    return { 
      __html: text
        .replace(/<span>(.*?)<\/span>/g, '<span class="text-brand">$1</span>')
        .replace(/<span class=['"]text-brand-accent['"]>(.*?)<\/span>/g, '<span class="text-brand-accent">$1</span>')
    };
  };

  return (
    <section className={`py-20 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center">
          <div className="lg:w-1/2 lg:pr-12 mb-10 lg:mb-0">
            {smallText && (
              <p className="text-brand-accent font-semibold tracking-wider mb-4 uppercase">
                {smallText}
              </p>
            )}
            <h2 
              className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6"
              dangerouslySetInnerHTML={renderTitle(title)}
            />
            <p className="text-lg text-gray-700 mb-6">
              {description}
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start">
                  <div className="w-10 h-10 rounded-full bg-brand-light flex items-center justify-center mr-4">
                    <feature.icon className="h-5 w-5 text-brand" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
                    <p className="text-gray-700 text-sm">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link to={primaryButtonLink}>
                <Button className="bg-brand hover:bg-brand-secondary">
                  {primaryButtonText}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              {secondaryButtonText && (
                <Link to={secondaryButtonLink}>
                  <Button variant="outline" className="border-brand text-brand hover:bg-brand-light">
                    {secondaryButtonText}
                  </Button>
                </Link>
              )}
            </div>
          </div>
          
          <div className="lg:w-1/2 flex justify-center items-center">
            <div className="bg-white p-8 rounded-xl shadow-lg w-full max-w-md border border-gray-100">
              <div className="text-center">
                {(statValue && statText) && (
                  <div className="bg-brand-light/30 p-6 rounded-lg">
                    <div className="text-center">
                      <span className="text-brand text-4xl font-bold">{statValue}</span>
                      <p className="text-gray-700 mt-2">{statText}</p>
                    </div>
                  </div>
                )}
                
                <div className="mt-6">
                  <h3 className="text-2xl font-bold mb-3">Conecte-se à Nossa Rede</h3>
                  <p className="text-gray-600 mb-6">
                    Agende uma reunião com nossa equipe para conhecer mais sobre como podemos conectar seu negócio aos investidores certos.
                  </p>
                  <Link to="/contato">
                    <Button className="w-full bg-brand hover:bg-brand-secondary">
                      Agendar Conversa
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InvestorNetwork;
