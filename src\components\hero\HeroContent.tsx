
import { <PERSON>R<PERSON>, CheckCircle, LucideIcon } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

interface Stat {
  text: string;
  icon: LucideIcon;
}

interface HeroContentProps {
  smallText?: string;
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  stats: Stat[];
}

const HeroContent = ({
  smallText,
  title,
  description,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink,
  stats,
}: HeroContentProps) => {
  // Parse HTML in title
  const renderTitle = (text: string) => {
    return { 
      __html: text
        .replace(/<span>(.*?)<\/span>/g, '<span class="text-brand">$1</span>')
        .replace(/<span class="text-brand-accent">(.*?)<\/span>/g, '<span class="text-brand-accent">$1</span>')
    };
  };

  return (
    <div className="lg:w-1/2 mb-10 lg:mb-0 lg:pr-10 animate-fade-in">
      {smallText && (
        <p className="text-brand-accent font-semibold tracking-wider mb-4 uppercase">
          {smallText}
        </p>
      )}
      <h1 
        className="font-montserrat text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight"
        dangerouslySetInnerHTML={renderTitle(title)}
      />
      <p className="text-xl text-gray-700 mb-8 max-w-lg">
        {description}
      </p>
      <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 mb-8">
        <Link to={primaryButtonLink}>
          <Button className="w-full sm:w-auto bg-brand hover:bg-brand-secondary text-white px-8 py-6 text-lg font-medium">
            {primaryButtonText}
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </Link>
        {secondaryButtonText && secondaryButtonLink && (
          <Link to={secondaryButtonLink}>
            <Button className="w-full sm:w-auto bg-brand hover:bg-brand-secondary text-white px-8 py-6 text-lg font-medium">
              {secondaryButtonText}
            </Button>
          </Link>
        )}
      </div>
      {stats.length > 0 && (
        <div className="mt-8">
          <div className="flex flex-wrap gap-5">
            {stats.map((stat, index) => (
              <div key={index} className="flex items-center">
                {stat.icon && <stat.icon className={`h-5 w-5 ${index % 2 === 0 ? 'text-brand' : 'text-brand-accent'} mr-2`} />}
                <span className="text-gray-700">{stat.text}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default HeroContent;
