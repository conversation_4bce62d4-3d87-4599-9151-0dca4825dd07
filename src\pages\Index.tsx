
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import Features from "@/components/Features";
import Courses from "@/components/Courses";
import Testimonials from "@/components/Testimonials";
import InvestorNetwork from "@/components/InvestorNetwork";
import CallToAction from "@/components/CallToAction";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { BarChart2, FileSpreadsheet, ArrowRight, ShoppingCart } from "lucide-react";

const Index = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "ATAC Academy - Aceleração e Empreendedorismo";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-16">
        <Hero
          primaryButtonText="Acesse o Portal"
          primaryButtonLink="/dashboard"
        />
        <Features />

        {/* Portal Features Section */}
        <section className="py-24 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Portal de <span className="gradient-text">Crescimento</span> Inteligente
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Mais que cursos e mentoria: uma plataforma completa para acelerar sua jornada empreendedora com metodologia comprovada.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-6">
                    <BarChart2 className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Diagnósticos Inteligentes
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Avalie o estágio da sua startup com nossos assessments de maturidade e valuation baseados em dados reais do mercado.
                  </p>
                  <Link to="/diagnostic">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 py-3">
                      Fazer Diagnóstico
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>

              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                    <FileSpreadsheet className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Jornada Personalizada
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Receba um roadmap personalizado baseado no seu assessment, com tarefas, recursos e marcos específicos para seu estágio.
                  </p>
                  <Link to="/personalized-journey">
                    <Button className="w-full bg-green-600 hover:bg-green-700 py-3">
                      Ver Minha Jornada
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>

              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mb-6">
                    <ShoppingCart className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Ferramentas Práticas
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Acesse templates, planilhas e ferramentas de gestão desenvolvidas por especialistas para acelerar sua operação.
                  </p>
                  <Link to="/management-tools">
                    <Button className="w-full bg-purple-600 hover:bg-purple-700 py-3">
                      Explorar Ferramentas
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>

              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center mb-6">
                    <BarChart2 className="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Biblioteca de Conteúdo
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Acesse artigos, vídeos, templates e cursos exclusivos organizados por categoria e nível de dificuldade.
                  </p>
                  <Link to="/content-library">
                    <Button className="w-full bg-orange-600 hover:bg-orange-700 py-3">
                      Explorar Conteúdo
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>

              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-brand-light flex items-center justify-center mb-6">
                    <BarChart2 className="h-8 w-8 text-brand" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Networking Qualificado
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Conecte-se com empreendedores, investidores e especialistas do ecossistema ATAC em eventos e oportunidades exclusivas.
                  </p>
                  <Link to="/networking-hub">
                    <Button className="w-full bg-brand hover:bg-brand-secondary py-3">
                      Fazer Networking
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>

              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-brand-accent/20 flex items-center justify-center mb-6">
                    <BarChart2 className="h-8 w-8 text-brand-accent" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Acompanhamento de Resultados
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Monitore seu progresso com métricas e indicadores personalizados, recebendo insights para otimizar seu crescimento.
                  </p>
                  <Link to="/dashboard">
                    <Button className="w-full bg-brand-accent hover:bg-brand-accent/90 py-3">
                      Ver Dashboard
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        <Courses />
        
        {/* New Diagnostic Section */}
        <section className="py-24 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Diagnósticos <span className="gradient-text">Exclusivos</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Ferramentas de análise desenvolvidas por especialistas para impulsionar sua jornada empreendedora.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-brand-light flex items-center justify-center mb-6">
                    <BarChart2 className="h-8 w-8 text-brand" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Diagnóstico de Valuation
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Descubra o valor estimado do seu negócio com nossa ferramenta de diagnóstico baseada em métricas de mercado e indicadores financeiros.
                  </p>
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-gray-900">R$ 1297</span>
                    <span className="text-gray-500 ml-2">pagamento único</span>
                  </div>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-brand mr-3 flex-shrink-0"></div>
                      <span>Análise de indicadores financeiros</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-brand mr-3 flex-shrink-0"></div>
                      <span>Comparativo com mercado</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-brand mr-3 flex-shrink-0"></div>
                      <span>Projeção de crescimento</span>
                    </div>
                  </div>
                  <Link to="/diagnostic">
                    <Button className="w-full bg-brand hover:bg-brand-secondary py-5">
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      Adquirir Diagnóstico
                    </Button>
                  </Link>
                </div>
              </div>
              
              <div className="pricing-card">
                <div className="p-8">
                  <div className="w-16 h-16 rounded-full bg-brand-accent/20 flex items-center justify-center mb-6">
                    <FileSpreadsheet className="h-8 w-8 text-brand-accent" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    Assessment de Maturidade
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Identifique o nível de maturidade da sua startup e receba recomendações personalizadas para avançar para o próximo estágio.
                  </p>
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-gray-900">R$ 347</span>
                    <span className="text-gray-500 ml-2">pagamento único</span>
                  </div>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-brand-accent mr-3 flex-shrink-0"></div>
                      <span>Análise de processo e operações</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-brand-accent mr-3 flex-shrink-0"></div>
                      <span>Avaliação de modelo de negócio</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-brand-accent mr-3 flex-shrink-0"></div>
                      <span>Estratégia de crescimento</span>
                    </div>
                  </div>
                  <Link to="/diagnostic">
                    <Button className="w-full bg-brand-accent hover:bg-brand-accent/90 py-5">
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      Adquirir Assessment
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
            
            <div className="mt-12 text-center">
              <Link to="/diagnostic">
                <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50">
                  Ver detalhes dos diagnósticos
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
        
        <Testimonials />
        <InvestorNetwork />
        <CallToAction />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
