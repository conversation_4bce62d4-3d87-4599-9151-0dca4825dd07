import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  Target, 
  BarChart3, 
  Calendar, 
  Users, 
  DollarSign, 
  TrendingUp,
  FileText,
  CheckSquare,
  Clock,
  Download,
  PlayCircle,
  Lightbulb,
  Building,
  Rocket,
  Award,
  Star,
  ArrowRight
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Mock tools data
const managementTools = [
  {
    id: 1,
    name: "Business Model Canvas",
    category: "Estratégia",
    type: "template",
    description: "Template interativo para estruturar e visualizar seu modelo de negócio de forma clara e objetiva.",
    features: ["Template editável", "<PERSON><PERSON><PERSON> de preenchimento", "Exemplos práticos", "Exportação em PDF"],
    difficulty: "Iniciante",
    estimatedTime: "30-45 min",
    downloads: 1250,
    rating: 4.8,
    popular: true,
    preview: "/preview-canvas.jpg"
  },
  {
    id: 2,
    name: "OKR Tracker",
    category: "Gestão",
    type: "ferramenta",
    description: "Sistema para definir, acompanhar e medir Objectives and Key Results da sua startup.",
    features: ["Dashboard de métricas", "Acompanhamento em tempo real", "Relatórios automáticos", "Integração com equipe"],
    difficulty: "Intermediário",
    estimatedTime: "1-2 horas",
    downloads: 890,
    rating: 4.9,
    popular: true,
    preview: "/preview-okr.jpg"
  },
  {
    id: 3,
    name: "Financial Planning Spreadsheet",
    category: "Financeiro",
    type: "planilha",
    description: "Planilha completa para planejamento financeiro, projeções e controle de fluxo de caixa.",
    features: ["Projeções automáticas", "Gráficos dinâmicos", "Cenários múltiplos", "Indicadores financeiros"],
    difficulty: "Avançado",
    estimatedTime: "2-3 horas",
    downloads: 650,
    rating: 4.7,
    popular: false,
    preview: "/preview-financial.jpg"
  },
  {
    id: 4,
    name: "Lean Startup Validation Board",
    category: "Validação",
    type: "template",
    description: "Framework para validar hipóteses de negócio usando metodologia Lean Startup.",
    features: ["Canvas de hipóteses", "Métricas de validação", "Experimentos estruturados", "Aprendizados documentados"],
    difficulty: "Intermediário",
    estimatedTime: "45-60 min",
    downloads: 1100,
    rating: 4.6,
    popular: true,
    preview: "/preview-lean.jpg"
  },
  {
    id: 5,
    name: "Pitch Deck Template",
    category: "Captação",
    type: "template",
    description: "Template profissional para criar apresentações que conquistam investidores.",
    features: ["20 slides estruturados", "Design profissional", "Guia de conteúdo", "Exemplos de sucesso"],
    difficulty: "Iniciante",
    estimatedTime: "1-2 horas",
    downloads: 2100,
    rating: 4.9,
    popular: true,
    preview: "/preview-pitch.jpg"
  },
  {
    id: 6,
    name: "Customer Journey Mapper",
    category: "Marketing",
    type: "ferramenta",
    description: "Ferramenta para mapear e otimizar a jornada do cliente em todos os pontos de contato.",
    features: ["Mapeamento visual", "Pontos de dor identificados", "Oportunidades de melhoria", "Personas integradas"],
    difficulty: "Intermediário",
    estimatedTime: "1-1.5 horas",
    downloads: 780,
    rating: 4.5,
    popular: false,
    preview: "/preview-journey.jpg"
  }
];

const categories = ["Todos", "Estratégia", "Gestão", "Financeiro", "Validação", "Captação", "Marketing"];
const toolTypes = ["Todos", "template", "ferramenta", "planilha"];
const difficulties = ["Todos", "Iniciante", "Intermediário", "Avançado"];

const ManagementTools = () => {
  const [selectedCategory, setSelectedCategory] = useState("Todos");
  const [selectedType, setSelectedType] = useState("Todos");
  const [selectedDifficulty, setSelectedDifficulty] = useState("Todos");
  const [filteredTools, setFilteredTools] = useState(managementTools);

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Ferramentas de Gestão | ATAC Academy";
  }, []);

  useEffect(() => {
    let filtered = managementTools;

    if (selectedCategory !== "Todos") {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    if (selectedType !== "Todos") {
      filtered = filtered.filter(tool => tool.type === selectedType);
    }

    if (selectedDifficulty !== "Todos") {
      filtered = filtered.filter(tool => tool.difficulty === selectedDifficulty);
    }

    setFilteredTools(filtered);
  }, [selectedCategory, selectedType, selectedDifficulty]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "template":
        return <FileText className="h-4 w-4" />;
      case "ferramenta":
        return <Target className="h-4 w-4" />;
      case "planilha":
        return <BarChart3 className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "template":
        return "Template";
      case "ferramenta":
        return "Ferramenta";
      case "planilha":
        return "Planilha";
      default:
        return type;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Iniciante":
        return "bg-green-100 text-green-800";
      case "Intermediário":
        return "bg-yellow-100 text-yellow-800";
      case "Avançado":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const popularTools = managementTools.filter(tool => tool.popular);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ferramentas de <span className="text-brand">Gestão</span>
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Templates, planilhas e ferramentas práticas para acelerar o crescimento da sua startup
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-brand mb-1">{managementTools.length}</div>
                <div className="text-sm text-gray-600">Ferramentas disponíveis</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-brand-accent mb-1">6</div>
                <div className="text-sm text-gray-600">Categorias</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-green-600 mb-1">7.8k</div>
                <div className="text-sm text-gray-600">Downloads totais</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-purple-600 mb-1">4.7</div>
                <div className="text-sm text-gray-600">Avaliação média</div>
              </div>
            </div>
          </div>

          {/* Popular Tools */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Ferramentas Mais Populares</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {popularTools.slice(0, 3).map((tool) => (
                <Card key={tool.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {getTypeIcon(tool.type)}
                        <Badge variant="outline" className="ml-2">
                          {getTypeLabel(tool.type)}
                        </Badge>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">Popular</Badge>
                    </div>
                    <CardTitle className="text-lg">{tool.name}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {tool.estimatedTime}
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-1 text-yellow-500" />
                        {tool.rating}
                      </div>
                      <div className="flex items-center">
                        <Download className="h-4 w-4 mr-1" />
                        {tool.downloads}
                      </div>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      {tool.features.slice(0, 3).map((feature, index) => (
                        <div key={index} className="flex items-center text-sm">
                          <CheckSquare className="h-3 w-3 text-green-600 mr-2" />
                          {feature}
                        </div>
                      ))}
                    </div>
                    
                    <Button className="w-full">
                      <Download className="mr-2 h-4 w-4" />
                      Baixar Ferramenta
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <h3 className="text-lg font-semibold mb-4">Filtrar Ferramentas</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categoria
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand"
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand"
                >
                  {toolTypes.map((type) => (
                    <option key={type} value={type}>
                      {type === "Todos" ? "Todos" : getTypeLabel(type)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dificuldade
                </label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand"
                >
                  {difficulties.map((difficulty) => (
                    <option key={difficulty} value={difficulty}>
                      {difficulty}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Tools Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTools.map((tool) => (
              <Card key={tool.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {getTypeIcon(tool.type)}
                      <Badge variant="outline" className="ml-2">
                        {getTypeLabel(tool.type)}
                      </Badge>
                    </div>
                    <Badge className={getDifficultyColor(tool.difficulty)}>
                      {tool.difficulty}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{tool.name}</CardTitle>
                  <CardDescription>{tool.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {tool.estimatedTime}
                    </div>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 mr-1 text-yellow-500" />
                      {tool.rating}
                    </div>
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {tool.downloads}
                    </div>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <h4 className="font-medium text-sm">Recursos inclusos:</h4>
                    {tool.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm">
                        <CheckSquare className="h-3 w-3 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Download className="mr-2 h-4 w-4" />
                      Baixar
                    </Button>
                    <Button variant="outline" size="sm">
                      <PlayCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTools.length === 0 && (
            <div className="text-center py-12">
              <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhuma ferramenta encontrada
              </h3>
              <p className="text-gray-600">
                Tente ajustar os filtros para encontrar as ferramentas que você precisa
              </p>
            </div>
          )}

          {/* CTA Section */}
          <div className="max-w-4xl mx-auto mt-16">
            <div className="bg-gradient-to-r from-brand to-brand-accent rounded-xl p-8 text-white text-center">
              <h3 className="text-2xl font-bold mb-4">Precisa de uma ferramenta personalizada?</h3>
              <p className="text-lg mb-6 opacity-90">
                Nossa equipe pode criar ferramentas customizadas para as necessidades específicas da sua startup
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/contato">
                  <Button className="bg-white text-brand hover:bg-gray-100">
                    Solicitar Ferramenta Customizada
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link to="/mentoria">
                  <Button variant="outline" className="border-white text-white hover:bg-white hover:text-brand">
                    Falar com Especialista
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ManagementTools;
