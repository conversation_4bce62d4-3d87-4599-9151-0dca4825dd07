
import React from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { ArrowRight, Brain, Network, Zap, Lightbulb, Workflow, Users } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Framework = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero Section with Lavender Background */}
        <section className="bg-[#f0ecff] py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 font-montserrat leading-tight">
                <span className="text-black">Moldando Supermentes</span> <br />
                <span className="text-black">para o Futuro dos</span> <br />
                <span className="text-black">N<PERSON><PERSON><PERSON><PERSON></span>
              </h1>
              <p className="text-lg md:text-xl text-gray-700 mb-12 max-w-3xl mx-auto">
                Uma abordagem pioneira que integra diversos nós do ecossistema, potencializando a 
                inteligência coletiva e transformando a maneira como as organizações operam.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link to="/contato">
                  <Button className="bg-[#33C3F0] hover:bg-[#33C3F0]/90 text-white px-8 py-6 text-lg w-full sm:w-auto">
                    Junte-se a Nós
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/diagnostic">
                  <Button variant="outline" className="border-[#7E69AB] text-[#7E69AB] hover:bg-[#7E69AB]/10 px-8 py-6 text-lg w-full sm:w-auto">
                    Faça um Diagnóstico
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* What is a Supermind Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center gap-12">
              <div className="md:w-1/2">
                <Brain size={64} className="text-[#7E69AB] mb-6" />
                <h2 className="text-3xl md:text-4xl font-bold mb-6 font-montserrat">O Que é uma <span className="text-[#33C3F0]">Supermente</span>?</h2>
                <p className="text-lg text-gray-700 mb-6">
                  Uma supermente representa a sinergia de múltiplas inteligências individuais, colaborando de forma coordenada 
                  para alcançar objetivos comuns. Essa colaboração amplifica a capacidade de resolver problemas complexos, 
                  inovar e adaptar-se a mudanças rápidas no mercado.
                </p>
                <p className="text-lg text-gray-700">
                  Inspirado pelos conceitos de Thomas Malone, do MIT, o ATAC Pro Framework emerge como uma solução 
                  pioneira que integra diversos nós do ecossistema de inovação.
                </p>
              </div>
              <div className="md:w-1/2 flex justify-center">
                <div className="relative w-full max-w-md">
                  <div className="absolute top-0 left-0 w-full h-full bg-[#7E69AB]/10 rounded-3xl transform rotate-3"></div>
                  <div className="relative z-10 bg-white p-8 rounded-3xl shadow-xl border border-gray-100">
                    <Network size={48} className="text-[#33C3F0] mb-6" />
                    <h3 className="text-2xl font-bold mb-4">Inteligência Coletiva</h3>
                    <p className="text-gray-600">
                      Quando mentes diversas trabalham juntas de maneira estruturada, alcançamos resultados 
                      exponencialmente superiores ao que indivíduos poderiam realizar isoladamente.
                    </p>
                    <div className="mt-6 pt-6 border-t border-gray-100">
                      <p className="text-[#7E69AB] font-semibold italic">
                        "O todo é maior que a soma das partes quando as partes interagem."
                      </p>
                      <p className="text-gray-500 text-sm mt-2">— Thomas Malone, MIT</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* ATAC Pro Framework Role */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 font-montserrat">O Papel do <span className="text-[#7E69AB]">ATAC Pro</span> Framework</h2>
              <p className="text-lg text-gray-700 max-w-3xl mx-auto">
                O ATAC Pro Framework atua como um catalisador na formação dessas supermentes, oferecendo uma estrutura 
                que conecta diferentes stakeholders, facilitando a troca de conhecimentos, recursos e oportunidades.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <Zap size={48} className="text-[#33C3F0] mb-4" />
                <h3 className="text-xl font-bold mb-3">Conexão de Ecossistemas</h3>
                <p className="text-gray-600">
                  Criamos pontes entre startups, corporações, investidores e academia, formando um ecossistema integrado.
                </p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <Lightbulb size={48} className="text-[#33C3F0] mb-4" />
                <h3 className="text-xl font-bold mb-3">Catalisação de Ideias</h3>
                <p className="text-gray-600">
                  Proporcionamos o ambiente ideal para que ideias inovadoras floresçam e se transformem em soluções concretas.
                </p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <Workflow size={48} className="text-[#33C3F0] mb-4" />
                <h3 className="text-xl font-bold mb-3">Metodologia Estruturada</h3>
                <p className="text-gray-600">
                  Oferecemos frameworks e metodologias que orientam o processo de inovação de forma sistemática e eficiente.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-10 text-center font-montserrat">
                Benefícios de Adotar o <span className="text-[#7E69AB]">ATAC Pro</span> Framework
              </h2>
              
              <div className="space-y-8">
                <div className="flex items-start gap-4">
                  <div className="bg-[#7E69AB]/10 p-3 rounded-full">
                    <Zap size={24} className="text-[#7E69AB]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Inovação Acelerada</h3>
                    <p className="text-gray-700">
                      A colaboração contínua entre diversos atores do ecossistema estimula a geração de ideias disruptivas.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-[#7E69AB]/10 p-3 rounded-full">
                    <Brain size={24} className="text-[#7E69AB]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Tomada de Decisão Informada</h3>
                    <p className="text-gray-700">
                      O acesso a uma rede ampla de conhecimentos e experiências permite decisões mais estratégicas e assertivas.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-[#7E69AB]/10 p-3 rounded-full">
                    <Users size={24} className="text-[#7E69AB]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Crescimento Sustentável</h3>
                    <p className="text-gray-700">
                      A sinergia entre os participantes do ecossistema cria um ciclo virtuoso de desenvolvimento e expansão.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 bg-[#7E69AB] text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-montserrat">Seja Parte da Revolução</h2>
            <p className="text-xl mb-10 max-w-2xl mx-auto">
              Não fique à margem dessa transformação. Integre-se ao ATAC Pro Framework e contribua para a 
              construção de uma supermente que redefinirá o futuro dos negócios.
            </p>
            <div className="inline-block">
              <Link to="/contato">
                <Button className="bg-white text-[#7E69AB] hover:bg-gray-100 px-8 py-6 text-lg">
                  Entre em Contato
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
            <p className="mt-10 text-lg font-medium">
              O futuro pertence àqueles que colaboram. Conecte-se. Inove. Cresça.
            </p>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Framework;
