import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  BookOpen, 
  PlayCircle, 
  Download, 
  Clock, 
  Star, 
  Filter,
  Search,
  Tag,
  TrendingUp,
  Users,
  Target,
  Lightbulb,
  Building,
  DollarSign,
  BarChart3
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Mock content data
const contentLibrary = [
  {
    id: 1,
    title: "Como validar sua ideia de negócio em 30 dias",
    type: "article",
    category: "Validação",
    duration: "15 min",
    difficulty: "Iniciante",
    rating: 4.8,
    views: 1250,
    description: "<PERSON><PERSON>a completo para validar sua ideia de negócio usando metodologias ágeis e feedback de clientes reais.",
    tags: ["validação", "mvp", "pesquisa de mercado"],
    author: "Dra. Sofia Oliveira",
    publishedAt: "2024-01-15",
    featured: true
  },
  {
    id: 2,
    title: "Workshop: Criando um Pitch Deck Irresistível",
    type: "video",
    category: "Captação",
    duration: "45 min",
    difficulty: "Intermediário",
    rating: 4.9,
    views: 890,
    description: "Aprenda a criar um pitch deck que conquista investidores com técnicas comprovadas e exemplos reais.",
    tags: ["pitch", "investimento", "apresentação"],
    author: "Carlos Mendes",
    publishedAt: "2024-01-10",
    featured: true
  },
  {
    id: 3,
    title: "Template: Business Model Canvas",
    type: "template",
    category: "Estratégia",
    duration: "5 min",
    difficulty: "Iniciante",
    rating: 4.7,
    views: 2100,
    description: "Template editável do Business Model Canvas para estruturar seu modelo de negócio.",
    tags: ["canvas", "modelo de negócio", "estratégia"],
    author: "Equipe ATAC",
    publishedAt: "2024-01-08",
    featured: false
  },
  {
    id: 4,
    title: "Métricas que Todo Empreendedor Deve Acompanhar",
    type: "article",
    category: "Gestão",
    duration: "20 min",
    difficulty: "Intermediário",
    rating: 4.6,
    views: 1580,
    description: "Descubra as métricas essenciais para acompanhar o crescimento e saúde do seu negócio.",
    tags: ["métricas", "kpis", "análise"],
    author: "Ana Costa",
    publishedAt: "2024-01-05",
    featured: false
  },
  {
    id: 5,
    title: "Curso: Growth Hacking para Startups",
    type: "course",
    category: "Marketing",
    duration: "2h 30min",
    difficulty: "Avançado",
    rating: 4.9,
    views: 650,
    description: "Curso completo sobre estratégias de growth hacking para acelerar o crescimento da sua startup.",
    tags: ["growth", "marketing", "aquisição"],
    author: "Roberto Lima",
    publishedAt: "2024-01-01",
    featured: true
  },
  {
    id: 6,
    title: "Checklist: Aspectos Jurídicos para Startups",
    type: "checklist",
    category: "Jurídico",
    duration: "10 min",
    difficulty: "Iniciante",
    rating: 4.5,
    views: 980,
    description: "Lista completa dos aspectos jurídicos que toda startup deve considerar desde o início.",
    tags: ["jurídico", "compliance", "estruturação"],
    author: "Dr. João Santos",
    publishedAt: "2023-12-28",
    featured: false
  }
];

const categories = ["Todos", "Validação", "Captação", "Estratégia", "Gestão", "Marketing", "Jurídico"];
const contentTypes = ["Todos", "article", "video", "template", "course", "checklist"];
const difficulties = ["Todos", "Iniciante", "Intermediário", "Avançado"];

const ContentLibrary = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Todos");
  const [selectedType, setSelectedType] = useState("Todos");
  const [selectedDifficulty, setSelectedDifficulty] = useState("Todos");
  const [filteredContent, setFilteredContent] = useState(contentLibrary);

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Biblioteca de Conteúdo | ATAC Academy";
  }, []);

  useEffect(() => {
    let filtered = contentLibrary;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== "Todos") {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by type
    if (selectedType !== "Todos") {
      filtered = filtered.filter(item => item.type === selectedType);
    }

    // Filter by difficulty
    if (selectedDifficulty !== "Todos") {
      filtered = filtered.filter(item => item.difficulty === selectedDifficulty);
    }

    setFilteredContent(filtered);
  }, [searchTerm, selectedCategory, selectedType, selectedDifficulty]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "article":
        return <BookOpen className="h-4 w-4" />;
      case "video":
        return <PlayCircle className="h-4 w-4" />;
      case "template":
        return <Download className="h-4 w-4" />;
      case "course":
        return <Target className="h-4 w-4" />;
      case "checklist":
        return <BarChart3 className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "article":
        return "Artigo";
      case "video":
        return "Vídeo";
      case "template":
        return "Template";
      case "course":
        return "Curso";
      case "checklist":
        return "Checklist";
      default:
        return type;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Iniciante":
        return "bg-green-100 text-green-800";
      case "Intermediário":
        return "bg-yellow-100 text-yellow-800";
      case "Avançado":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const featuredContent = contentLibrary.filter(item => item.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Biblioteca de <span className="text-brand">Conteúdo</span>
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Acesso a conteúdos exclusivos, templates e recursos para acelerar seu crescimento
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-brand mb-1">{contentLibrary.length}</div>
                <div className="text-sm text-gray-600">Recursos disponíveis</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-brand-accent mb-1">6</div>
                <div className="text-sm text-gray-600">Categorias</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-green-600 mb-1">15h</div>
                <div className="text-sm text-gray-600">Conteúdo total</div>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="text-2xl font-bold text-purple-600 mb-1">4.8</div>
                <div className="text-sm text-gray-600">Avaliação média</div>
              </div>
            </div>
          </div>

          {/* Featured Content */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Conteúdo em Destaque</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {featuredContent.map((item) => (
                <Card key={item.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {getTypeIcon(item.type)}
                        <Badge variant="outline" className="ml-2">
                          {getTypeLabel(item.type)}
                        </Badge>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">Destaque</Badge>
                    </div>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {item.duration}
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-1 text-yellow-500" />
                        {item.rating}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1 mb-4">
                      {item.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <Button className="w-full">
                      Acessar Conteúdo
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Buscar
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar conteúdo..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Categoria
                </label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo
                </label>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {contentTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type === "Todos" ? "Todos" : getTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dificuldade
                </label>
                <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {difficulties.map((difficulty) => (
                      <SelectItem key={difficulty} value={difficulty}>
                        {difficulty}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredContent.map((item) => (
              <Card key={item.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {getTypeIcon(item.type)}
                      <Badge variant="outline" className="ml-2">
                        {getTypeLabel(item.type)}
                      </Badge>
                    </div>
                    <Badge className={getDifficultyColor(item.difficulty)}>
                      {item.difficulty}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {item.duration}
                    </div>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 mr-1 text-yellow-500" />
                      {item.rating}
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {item.views}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-4">
                    Por {item.author} • {new Date(item.publishedAt).toLocaleDateString('pt-BR')}
                  </div>
                  
                  <Button className="w-full" variant="outline">
                    Acessar Conteúdo
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredContent.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhum conteúdo encontrado
              </h3>
              <p className="text-gray-600">
                Tente ajustar os filtros ou termos de busca
              </p>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ContentLibrary;
