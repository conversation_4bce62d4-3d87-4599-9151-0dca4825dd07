import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  CheckCircle, 
  Circle, 
  Clock, 
  Target, 
  ArrowRight, 
  BookOpen, 
  Users, 
  MessageSquare,
  Award,
  Lightbulb,
  TrendingUp,
  Building,
  Rocket,
  Star,
  Calendar,
  Download
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

// Mock journey data based on user's assessment results
const journeyData = {
  currentPhase: "Validação de Mercado",
  overallProgress: 65,
  phases: [
    {
      id: 1,
      title: "Ideação e Conceito",
      status: "completed",
      progress: 100,
      description: "Desenvolvimento da ideia inicial e validação do conceito",
      tasks: [
        { id: 1, title: "Definir problema a ser resolvido", completed: true },
        { id: 2, title: "Pesquisar mercado e concorrência", completed: true },
        { id: 3, title: "Criar proposta de valor", completed: true },
        { id: 4, title: "Validar hipóteses iniciais", completed: true }
      ],
      resources: [
        { type: "article", title: "Como validar sua ideia de negócio", url: "#" },
        { type: "video", title: "Workshop: Criando uma proposta de valor", url: "#" }
      ]
    },
    {
      id: 2,
      title: "Validação de Mercado",
      status: "current",
      progress: 65,
      description: "Teste e validação da solução com clientes reais",
      tasks: [
        { id: 5, title: "Criar MVP (Produto Mínimo Viável)", completed: true },
        { id: 6, title: "Recrutar primeiros usuários beta", completed: true },
        { id: 7, title: "Coletar feedback dos usuários", completed: true },
        { id: 8, title: "Iterar baseado no feedback", completed: false },
        { id: 9, title: "Definir métricas de sucesso", completed: false },
        { id: 10, title: "Alcançar product-market fit", completed: false }
      ],
      resources: [
        { type: "article", title: "Guia completo para criar um MVP", url: "#" },
        { type: "template", title: "Template de pesquisa com usuários", url: "#" },
        { type: "mentoring", title: "Sessão: Interpretando feedback de usuários", url: "#" }
      ]
    },
    {
      id: 3,
      title: "Estruturação do Negócio",
      status: "upcoming",
      progress: 0,
      description: "Formalização e estruturação legal e operacional",
      tasks: [
        { id: 11, title: "Definir modelo de negócio", completed: false },
        { id: 12, title: "Estruturar aspectos legais", completed: false },
        { id: 13, title: "Implementar processos operacionais", completed: false },
        { id: 14, title: "Montar equipe inicial", completed: false }
      ],
      resources: [
        { type: "article", title: "Modelos de negócio para startups", url: "#" },
        { type: "checklist", title: "Checklist jurídico para startups", url: "#" }
      ]
    },
    {
      id: 4,
      title: "Captação de Investimento",
      status: "upcoming",
      progress: 0,
      description: "Preparação e execução da captação de recursos",
      tasks: [
        { id: 15, title: "Preparar pitch deck", completed: false },
        { id: 16, title: "Definir valuation", completed: false },
        { id: 17, title: "Identificar investidores", completed: false },
        { id: 18, title: "Executar roadshow", completed: false }
      ],
      resources: [
        { type: "template", title: "Template de pitch deck", url: "#" },
        { type: "course", title: "Curso: Captação de investimento", url: "#" }
      ]
    },
    {
      id: 5,
      title: "Crescimento e Escala",
      status: "upcoming",
      progress: 0,
      description: "Expansão e crescimento sustentável do negócio",
      tasks: [
        { id: 19, title: "Implementar estratégias de growth", completed: false },
        { id: 20, title: "Expandir equipe", completed: false },
        { id: 21, title: "Otimizar operações", completed: false },
        { id: 22, title: "Explorar novos mercados", completed: false }
      ],
      resources: [
        { type: "article", title: "Estratégias de growth hacking", url: "#" },
        { type: "course", title: "Curso: Gestão de crescimento", url: "#" }
      ]
    }
  ]
};

const PersonalizedJourney = () => {
  const [selectedPhase, setSelectedPhase] = useState(2); // Current phase

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Jornada Personalizada | ATAC Academy";
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      case "current":
        return <Clock className="h-6 w-6 text-blue-600" />;
      default:
        return <Circle className="h-6 w-6 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "border-green-500 bg-green-50";
      case "current":
        return "border-blue-500 bg-blue-50";
      default:
        return "border-gray-300 bg-gray-50";
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case "article":
        return <BookOpen className="h-4 w-4" />;
      case "video":
        return <Target className="h-4 w-4" />;
      case "template":
        return <Download className="h-4 w-4" />;
      case "course":
        return <Award className="h-4 w-4" />;
      case "mentoring":
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Sua Jornada de <span className="text-brand">Crescimento</span>
            </h1>
            <p className="text-xl text-gray-700 mb-6">
              Roadmap personalizado baseado no seu Assessment de Maturidade
            </p>
            
            {/* Overall Progress */}
            <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-4">
                <span className="font-semibold">Progresso Geral</span>
                <Badge variant="outline">{journeyData.overallProgress}% completo</Badge>
              </div>
              <Progress value={journeyData.overallProgress} className="h-3" />
              <p className="text-sm text-gray-600 mt-2">
                Fase atual: {journeyData.currentPhase}
              </p>
            </div>
          </div>

          {/* Journey Timeline */}
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left: Phase Navigation */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Fases da Jornada</CardTitle>
                    <CardDescription>
                      Clique em uma fase para ver os detalhes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {journeyData.phases.map((phase) => (
                        <button
                          key={phase.id}
                          onClick={() => setSelectedPhase(phase.id)}
                          className={`w-full p-4 rounded-lg border-2 transition-all text-left ${
                            selectedPhase === phase.id
                              ? "border-brand bg-brand/5"
                              : getStatusColor(phase.status)
                          }`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              {getStatusIcon(phase.status)}
                              <span className="ml-3 font-semibold text-sm">
                                Fase {phase.id}
                              </span>
                            </div>
                            <span className="text-xs text-gray-600">
                              {phase.progress}%
                            </span>
                          </div>
                          <h3 className="font-medium text-sm mb-1">{phase.title}</h3>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              className="bg-gradient-to-r from-brand to-brand-accent h-1.5 rounded-full transition-all"
                              style={{ width: `${phase.progress}%` }}
                            />
                          </div>
                        </button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right: Phase Details */}
              <div className="lg:col-span-2">
                {journeyData.phases
                  .filter(phase => phase.id === selectedPhase)
                  .map(phase => (
                    <div key={phase.id} className="space-y-6">
                      {/* Phase Header */}
                      <Card>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              {getStatusIcon(phase.status)}
                              <div className="ml-4">
                                <CardTitle>Fase {phase.id}: {phase.title}</CardTitle>
                                <CardDescription>{phase.description}</CardDescription>
                              </div>
                            </div>
                            <Badge 
                              variant={phase.status === "completed" ? "default" : "outline"}
                              className={
                                phase.status === "completed" 
                                  ? "bg-green-100 text-green-800" 
                                  : phase.status === "current"
                                  ? "bg-blue-100 text-blue-800"
                                  : ""
                              }
                            >
                              {phase.status === "completed" ? "Concluída" : 
                               phase.status === "current" ? "Em Andamento" : "Próxima"}
                            </Badge>
                          </div>
                          <Progress value={phase.progress} className="mt-4" />
                        </CardHeader>
                      </Card>

                      {/* Tasks */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center">
                            <Target className="h-5 w-5 mr-2" />
                            Tarefas ({phase.tasks.filter(t => t.completed).length}/{phase.tasks.length})
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {phase.tasks.map((task) => (
                              <div
                                key={task.id}
                                className={`flex items-center p-3 rounded-lg border ${
                                  task.completed
                                    ? "bg-green-50 border-green-200"
                                    : "bg-gray-50 border-gray-200"
                                }`}
                              >
                                {task.completed ? (
                                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                                ) : (
                                  <Circle className="h-5 w-5 text-gray-400 mr-3" />
                                )}
                                <span className={`flex-1 ${
                                  task.completed ? "text-green-900" : "text-gray-900"
                                }`}>
                                  {task.title}
                                </span>
                                {!task.completed && phase.status === "current" && (
                                  <Button size="sm" variant="outline">
                                    Iniciar
                                  </Button>
                                )}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      {/* Resources */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center">
                            <BookOpen className="h-5 w-5 mr-2" />
                            Recursos Recomendados
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {phase.resources.map((resource, index) => (
                              <div
                                key={index}
                                className="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                              >
                                <div className="p-2 bg-white rounded-lg mr-3">
                                  {getResourceIcon(resource.type)}
                                </div>
                                <div className="flex-1">
                                  <h4 className="font-medium text-sm">{resource.title}</h4>
                                  <p className="text-xs text-gray-600 capitalize">{resource.type}</p>
                                </div>
                                <ArrowRight className="h-4 w-4 text-gray-400" />
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  ))}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="max-w-4xl mx-auto mt-12 text-center">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
              <h3 className="text-xl font-bold mb-4">Precisa de ajuda com sua jornada?</h3>
              <p className="text-gray-600 mb-6">
                Nossa equipe de especialistas está pronta para te apoiar em cada etapa
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/mentoria">
                  <Button className="bg-brand hover:bg-brand-secondary">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Agendar Mentoria
                  </Button>
                </Link>
                <Link to="/contato">
                  <Button variant="outline">
                    <Calendar className="mr-2 h-4 w-4" />
                    Falar com Especialista
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default PersonalizedJourney;
