
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import FloatingWhatsAppButton from "@/components/FloatingWhatsAppButton";
import AIAssistant from "@/components/AIAssistant";
import Index from "@/pages/Index";
import QuemSomos from "@/pages/QuemSomos";
import Cursos from "@/pages/Cursos";
import CourseLanding from "@/pages/CourseLanding";
import Mentoria from "@/pages/Mentoria";
import MentorProfile from "@/pages/MentorProfile";
import OpenInnovation from "@/pages/OpenInnovation";
import CorporateChallenges from "@/pages/CorporateChallenges";
import InnovationHubs from "@/pages/InnovationHubs";
import Investidores from "@/pages/Investidores";
import OportunidadesInvestidores from "@/pages/OportunidadesInvestidores";
import Valuation from "@/pages/Valuation";
import LearningExperience from "@/pages/LearningExperience";
import AcelereSeuNegocio from "@/pages/AcelereSeuNegocio";
import InscricaoPrograma from "@/pages/InscricaoPrograma";
import Partnerships from "@/pages/Partnerships";
import Diagnostic from "@/pages/Diagnostic";
import DueDiligence from "@/pages/DueDiligence";
import Contato from "@/pages/Contato";
import Login from "@/pages/Login";
import Inscrever from "@/pages/Inscrever";
import Blog from "@/pages/Blog";
import NotFound from "@/pages/NotFound";
import MaturityAssessment from "@/pages/MaturityAssessment";
import CasesSucesso from "@/pages/CasesSucesso";
import AtacProMind from "@/pages/AtacProMind";
import Framework from "@/pages/Framework";
import Podcast from "@/pages/Podcast";
import NaMidia from "@/pages/NaMidia";
import Dashboard from "@/pages/Dashboard";
import PersonalizedJourney from "@/pages/PersonalizedJourney";
import ContentLibrary from "@/pages/ContentLibrary";
import NetworkingHub from "@/pages/NetworkingHub";
import ManagementTools from "@/pages/ManagementTools";

// WhatsApp button wrapper component that conditionally renders based on the current route
const WhatsAppButtonWrapper = () => {
  const location = useLocation();
  const hiddenOnRoutes = ['/maturity', '/maturity-assessment'];
  
  if (hiddenOnRoutes.includes(location.pathname)) {
    return null;
  }
  
  return <FloatingWhatsAppButton className="bottom-20" />;
};

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/quem-somos" element={<QuemSomos />} />
        <Route path="/cursos" element={<Cursos />} />
        <Route path="/curso/:courseId" element={<CourseLanding />} />
        <Route path="/curso-gestao-startup" element={<CourseLanding />} />
        <Route path="/startup-bootcamp" element={<CourseLanding />} />
        <Route path="/startup-revolution" element={<CourseLanding />} />
        <Route path="/ai-na-pratica" element={<CourseLanding />} />
        <Route path="/mentoria" element={<Mentoria />} />
        <Route path="/mentor/:id" element={<MentorProfile />} />
        <Route path="/open-innovation" element={<OpenInnovation />} />
        <Route path="/corporate-challenges" element={<CorporateChallenges />} />
        <Route path="/innovation-hubs" element={<InnovationHubs />} />
        <Route path="/investidores" element={<Investidores />} />
        <Route path="/investidores/oportunidades" element={<OportunidadesInvestidores />} />
        <Route path="/valuation" element={<Valuation />} />
        <Route path="/learning-experience" element={<LearningExperience />} />
        <Route path="/acelere-seu-negocio" element={<AcelereSeuNegocio />} />
        <Route path="/inscricao-programa" element={<InscricaoPrograma />} />
        <Route path="/parcerias" element={<Partnerships />} />
        <Route path="/diagnostic" element={<Diagnostic />} />
        <Route path="/maturity" element={<MaturityAssessment />} />
        <Route path="/due-diligence" element={<DueDiligence />} />
        <Route path="/contato" element={<Contato />} />
        <Route path="/login" element={<Login />} />
        <Route path="/inscrever" element={<Inscrever />} />
        <Route path="/blog" element={<Blog />} />
        <Route path="/blog/podcast" element={<Podcast />} />
        <Route path="/na-midia" element={<NaMidia />} />
        <Route path="/maturity-assessment" element={<MaturityAssessment />} />
        <Route path="/cases-sucesso" element={<CasesSucesso />} />
        <Route path="/open-innovation/cases" element={<CasesSucesso />} />
        <Route path="/atac-pro-mind" element={<AtacProMind />} />
        <Route path="/framework" element={<Framework />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/personalized-journey" element={<PersonalizedJourney />} />
        <Route path="/content-library" element={<ContentLibrary />} />
        <Route path="/networking-hub" element={<NetworkingHub />} />
        <Route path="/management-tools" element={<ManagementTools />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
      <WhatsAppButtonWrapper />
      <AIAssistant />
    </Router>
  );
}

export default App;
